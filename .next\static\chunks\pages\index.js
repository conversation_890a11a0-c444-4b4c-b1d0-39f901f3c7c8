/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cindex.js&page=%2F!":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cindex.js&page=%2F! ***!
  \**************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./pages/index.js */ \"./pages/index.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDcHktaWRlJTVDd3VkYWxhbmctc2hvcCU1Q3BhZ2VzJTVDaW5kZXguanMmcGFnZT0lMkYhLmpzIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMENBQWtCO0FBQ3pDO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz8xNDI0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9wYWdlcy9pbmRleC5qc1wiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cindex.js&page=%2F!\n"));

/***/ }),

/***/ "./components/CardGrid.js":
/*!********************************!*\
  !*** ./components/CardGrid.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CardGrid; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _PriceDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PriceDisplay */ \"./components/PriceDisplay.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction CardGrid(param) {\n    var _cards = param.cards, cards = _cards === void 0 ? [] : _cards, _enableInfiniteScroll = param.enableInfiniteScroll, enableInfiniteScroll = _enableInfiniteScroll === void 0 ? false : _enableInfiniteScroll, _categoryId = param.categoryId, categoryId = _categoryId === void 0 ? null : _categoryId, _featured = param.featured, featured = _featured === void 0 ? false : _featured, _categoryLoading = param.categoryLoading, categoryLoading = _categoryLoading === void 0 ? false : _categoryLoading;\n    var _this = this;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\"), sortBy = ref[0], setSortBy = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"purchase\"), priceType = ref1[0], setPriceType = ref1[1]; // 'purchase' 或 'estimated'\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(cards), allCards = ref2[0], setAllCards = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), loading = ref3[0], setLoading = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), hasMore = ref4[0], setHasMore = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1), page = ref5[0], setPage = ref5[1];\n    var observerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    var lastCardElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // 如果没有传入cards，显示空状态\n    var defaultCards = [];\n    // 初始化卡牌数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (cards.length > 0) {\n            setAllCards(cards);\n            setPage(1);\n            setHasMore(true);\n        }\n    }, [\n        cards\n    ]);\n    // 监听分类变化，重置状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (enableInfiniteScroll) {\n            setPage(1);\n            setHasMore(true);\n            // 如果有新的cards数据，使用新数据；否则保持当前数据\n            if (cards.length > 0) {\n                setAllCards(cards);\n            }\n        }\n    }, [\n        categoryId,\n        enableInfiniteScroll\n    ]);\n    // 加载更多卡牌的函数\n    var loadMoreCards = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function() {\n        var nextPage, url, response, data, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (loading || !hasMore || !enableInfiniteScroll) return [\n                        2\n                    ];\n                    setLoading(true);\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        5,\n                        6,\n                        7\n                    ]);\n                    nextPage = page + 1;\n                    url = new URL(\"/api/cards\", window.location.origin);\n                    url.searchParams.set(\"page\", nextPage.toString());\n                    url.searchParams.set(\"limit\", \"12\");\n                    if (featured) {\n                        url.searchParams.set(\"featured\", \"true\");\n                    }\n                    if (categoryId) {\n                        url.searchParams.set(\"category\", categoryId.toString());\n                    }\n                    return [\n                        4,\n                        fetch(url.toString())\n                    ];\n                case 2:\n                    response = _state.sent();\n                    if (!response.ok) return [\n                        3,\n                        4\n                    ];\n                    return [\n                        4,\n                        response.json()\n                    ];\n                case 3:\n                    data = _state.sent();\n                    if (data.cards && data.cards.length > 0) {\n                        setAllCards(function(prev) {\n                            return (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(prev).concat((0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(data.cards));\n                        });\n                        setPage(nextPage);\n                        setHasMore(data.pagination.hasNext);\n                    } else {\n                        setHasMore(false);\n                    }\n                    _state.label = 4;\n                case 4:\n                    return [\n                        3,\n                        7\n                    ];\n                case 5:\n                    error = _state.sent();\n                    console.error(\"Error loading more cards:\", error);\n                    return [\n                        3,\n                        7\n                    ];\n                case 6:\n                    setLoading(false);\n                    return [\n                        7\n                    ];\n                case 7:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        loading,\n        hasMore,\n        page,\n        categoryId,\n        enableInfiniteScroll\n    ]);\n    // Intersection Observer 回调\n    var lastCardElementCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(node) {\n        if (loading) return;\n        if (observerRef.current) observerRef.current.disconnect();\n        observerRef.current = new IntersectionObserver(function(entries) {\n            if (entries[0].isIntersecting && hasMore && enableInfiniteScroll) {\n                loadMoreCards();\n            }\n        }, {\n            threshold: 0.1,\n            rootMargin: \"200px\" // 提前200px开始加载，让用户感觉更流畅\n        });\n        if (node) observerRef.current.observe(node);\n    }, [\n        loading,\n        hasMore,\n        loadMoreCards,\n        enableInfiniteScroll\n    ]);\n    var displayCards = allCards.length > 0 ? allCards : defaultCards;\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), hoveredCard = ref6[0], setHoveredCard = ref6[1];\n    // 骨架屏组件\n    var SkeletonCard = function() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg overflow-hidden animate-shimmer h-full flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"aspect-w-3 aspect-h-4 bg-gray-200 h-48 sm:h-64\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                    lineNumber: 98,\n                    columnNumber: 7\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 sm:p-4 flex flex-col flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 11\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/2 mb-3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 11\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                            lineNumber: 100,\n                            columnNumber: 9\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-5 bg-gray-200 rounded w-1/3\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                            lineNumber: 104,\n                            columnNumber: 9\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                    lineNumber: 99,\n                    columnNumber: 7\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n            lineNumber: 97,\n            columnNumber: 5\n        }, _this);\n    };\n    var formatPrice = function(price) {\n        if (price === null || price === undefined || isNaN(price)) {\n            return \"RM0\";\n        }\n        return \"RM\".concat(Number(price).toLocaleString());\n    };\n    // 获取显示价格的函数\n    var getDisplayPrice = function(card) {\n        if (priceType === \"estimated\") {\n            return card.estimatedValue || card.price;\n        }\n        return card.price;\n    };\n    // 获取价格标签的函数\n    var getPriceLabel = function(card) {\n        if (priceType === \"estimated\") {\n            return \"Valuation\";\n        }\n        return card.status === \"Auctioning\" ? \"Current Price\" : \"Buy Price\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-6 sm:py-12 px-4 sm:px-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 sm:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 sm:mb-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.title\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base text-gray-600\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.subtitle\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center sm:justify-start mb-4 sm:mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-gray-100 rounded-lg p-1 shadow-sm w-full sm:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return setPriceType(\"purchase\");\n                                        },\n                                        className: \"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-3 rounded-md text-xs sm:text-sm font-bold transition-all duration-300 transform \".concat(priceType === \"purchase\" ? \"bg-white text-blue-600 shadow-md scale-105 ring-2 ring-blue-200\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"),\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.buyNowTab\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return setPriceType(\"estimated\");\n                                        },\n                                        className: \"flex-1 sm:flex-none px-4 sm:px-6 py-2 sm:py-3 rounded-md text-xs sm:text-sm font-bold transition-all duration-300 transform \".concat(priceType === \"estimated\" ? \"bg-white text-purple-600 shadow-md scale-105 ring-2 ring-purple-200\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"),\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.valuationTab\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 rounded-lg border-l-4 transition-all duration-300 \".concat(priceType === \"estimated\" ? \"bg-purple-50 border-purple-400 text-purple-800\" : \"bg-blue-50 border-blue-400 text-blue-800\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-bold\",\n                                children: [\n                                    \"Currently showing: \",\n                                    priceType === \"estimated\" ? (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.valuationMode\") : (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.buyNowMode\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm opacity-75\",\n                                children: priceType === \"estimated\" ? (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.valuationDesc\") : (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.buyNowDesc\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                displayCards.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 text-gray-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 5l7 7-7 7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 197,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"暂无商品\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mb-6\",\n                                children: \"目前还没有添加任何商品，请通过管理后台添加您的第一个商品。\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"\\uD83D\\uDCA1 提示：您可以通过管理后台添加钱币、纸币等收藏品\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"\\uD83D\\uDD27 管理后台地址：/admin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                        lineNumber: 194,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                    lineNumber: 193,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6\",\n                    children: [\n                        displayCards.map(function(card, index) {\n                            var isLastCard = index === displayCards.length - 1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: isLastCard && enableInfiniteScroll ? lastCardElementCallback : null,\n                                className: \"group cursor-pointer animate-fadeInUp\",\n                                style: {\n                                    animationDelay: \"\".concat(index % 12 * 100, \"ms\"),\n                                    animationFillMode: \"both\"\n                                },\n                                onMouseEnter: function() {\n                                    return setHoveredCard(card.id);\n                                },\n                                onMouseLeave: function() {\n                                    return setHoveredCard(null);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: priceType === \"estimated\" ? \"/valuation/\".concat(card.id) : \"/card/\".concat(card.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"block h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 h-full flex flex-col \".concat(hoveredCard === card.id ? \"shadow-2xl -translate-y-2\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: function() {\n                                                                if (Array.isArray(card.images) && card.images.length > 0) {\n                                                                    return card.images[0];\n                                                                } else if (typeof card.images === \"string\") {\n                                                                    try {\n                                                                        var parsedImages = JSON.parse(card.images);\n                                                                        return Array.isArray(parsedImages) && parsedImages.length > 0 ? parsedImages[0] : card.image;\n                                                                    } catch (e) {\n                                                                        return card.images;\n                                                                    }\n                                                                }\n                                                                return card.image;\n                                                            }(),\n                                                            alt: card.name,\n                                                            className: \"w-full h-48 sm:h-64 object-cover object-top transition-transform duration-500 \".concat(hoveredCard === card.id ? \"scale-110\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(card.status === \"auctioning\" ? \"bg-red-100 text-red-800\" : \"bg-green-100 text-green-800\"),\n                                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"coin.\".concat(card.status))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        card.endTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 bg-black/70 text-white px-2 py-1 rounded text-xs\",\n                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.formatTimeLeft)(card.endTime)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent transition-opacity duration-300 \".concat(hoveredCard === card.id ? \"opacity-100\" : \"opacity-0\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-4 left-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"block w-full bg-white text-gray-900 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors whitespace-nowrap text-center\",\n                                                                    onClick: function(e) {\n                                                                        e.preventDefault();\n                                                                        e.stopPropagation();\n                                                                        window.location.href = priceType === \"estimated\" ? \"/valuation/\".concat(card.id) : \"/card/\".concat(card.id);\n                                                                    },\n                                                                    children: priceType === \"estimated\" ? (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.startValuation\") : (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"home.viewDetails\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 sm:p-4 flex flex-col flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-gray-900 mb-1 text-sm sm:text-base line-clamp-2 min-h-[2.5rem] flex items-start\",\n                                                                    children: card.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs sm:text-sm text-gray-600 mb-2 sm:mb-3\",\n                                                                    children: [\n                                                                        card.year,\n                                                                        \" • \",\n                                                                        card.grade\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end justify-between mt-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PriceDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        price: card.price,\n                                                                        originalPrice: card.originalPrice,\n                                                                        estimatedValue: card.estimatedValue,\n                                                                        status: card.status,\n                                                                        showToggle: false,\n                                                                        defaultType: priceType,\n                                                                        currentType: priceType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"w-8 h-8 bg-gray-100 hover:bg-blue-100 rounded-full flex items-center justify-center transition-colors group flex-shrink-0 ml-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-4 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"ri-heart-line group-hover:text-blue-600 transition-colors\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 21\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 229,\n                                        columnNumber: 19\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, _this)\n                            }, card.id, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, _this);\n                        }),\n                        loading && enableInfiniteScroll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: Array.from({\n                                length: 8\n                            }).map(function(_, index) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-fadeInUp\",\n                                    style: {\n                                        animationDelay: \"\".concat(index * 100, \"ms\"),\n                                        animationFillMode: \"both\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonCard, {}, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 327,\n                                        columnNumber: 19\n                                    }, _this)\n                                }, \"skeleton-\".concat(index), false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, _this);\n                            })\n                        }, void 0, false)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this),\n                categoryLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-white bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-16 w-16 border-4 border-blue-200\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent absolute top-0 left-0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl font-medium text-gray-700 animate-pulse-slow\",\n                                        children: \"Switching category...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: \"Please wait a moment\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                        lineNumber: 337,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                    lineNumber: 336,\n                    columnNumber: 11\n                }, this),\n                enableInfiniteScroll && !loading && !hasMore && allCards.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-16 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-green-50 to-blue-50 px-6 py-3 rounded-full border border-green-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-green-500\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                        lineNumber: 355,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-700 font-medium\",\n                                    children: [\n                                        (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"coin.noMoreCoins\"),\n                                        \" (\",\n                                        allCards.length,\n                                        \" coins)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-sm mt-2\",\n                            children: \"Thank you for browsing!\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n                    lineNumber: 352,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CardGrid.js\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(CardGrid, \"N6dLNp2kxFOuKE9f8fHj2Hl4NO0=\");\n_c = CardGrid;\nvar _c;\n$RefreshReg$(_c, \"CardGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/CardGrid.js\n"));

/***/ }),

/***/ "./components/Header.js":
/*!******************************!*\
  !*** ./components/Header.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageSwitcher */ \"./components/LanguageSwitcher.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction Header(param) {\n    var siteConfig = param.siteConfig, currentPage = param.currentPage;\n    var _this = this;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), searchQuery = ref[0], setSearchQuery = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isMenuOpen = ref1[0], setIsMenuOpen = ref1[1];\n    var menuRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function handleClickOutside(event) {\n            if (menuRef.current && !menuRef.current.contains(event.target)) {\n                setIsMenuOpen(false);\n            }\n        };\n        if (isMenuOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isMenuOpen\n    ]);\n    // 使用默认值以防siteConfig未传入\n    var config = siteConfig || {\n        title: \"CoinMarket\",\n        logo: \"CoinMarket\",\n        navigation: []\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-4 sm:px-6 py-3 sm:py-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                ref: menuRef,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: function() {\n                                            return setIsMenuOpen(!isMenuOpen);\n                                        },\n                                        className: \"flex items-center justify-center w-10 h-10 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                        \"aria-label\": (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"header.menu\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-0.5 bg-gray-600 rounded-full transition-transform duration-200 \".concat(isMenuOpen ? \"rotate-45 translate-y-1.5\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-0.5 bg-gray-600 rounded-full transition-opacity duration-200 \".concat(isMenuOpen ? \"opacity-0\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-0.5 bg-gray-600 rounded-full transition-transform duration-200 \".concat(isMenuOpen ? \"-rotate-45 -translate-y-1.5\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in\",\n                                        children: currentPage === \"about\" ? // 在公司简介页面显示首页链接\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200\",\n                                                onClick: function() {\n                                                    return setIsMenuOpen(false);\n                                                },\n                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.home\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                                lineNumber: 61,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                            lineNumber: 60,\n                                            columnNumber: 21\n                                        }, this) : // 在其他页面显示公司简介链接\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/about\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200\",\n                                                onClick: function() {\n                                                    return setIsMenuOpen(false);\n                                                },\n                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.about\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                                lineNumber: 71,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                            lineNumber: 70,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"flex items-center hover:opacity-80 transition-opacity duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"/logo.png\",\n                                        alt: config.title || \"CoinMarket\",\n                                        className: \"h-8 sm:h-10 w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: config.navigation && config.navigation.length > 0 ? config.navigation.map(function(item) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 19\n                                }, _this)\n                            }, item.id, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 98,\n                                columnNumber: 17\n                            }, _this);\n                        }) : // 默认导航菜单（作为后备）\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/cards\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.allCoin\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 107,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/auction\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.auction\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/premium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.premium\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 118,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/buy-now\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.buyNow\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/sports-cards\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.sportsCards\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 128,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/trading-cards\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: \"Trading Cards\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 133,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/test-valuation\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-purple-600 hover:text-purple-700 transition-colors whitespace-nowrap font-medium\",\n                                        children: \"Valuation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 137,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/more\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: \"More\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative hidden sm:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"header.searchPlaceholder\"),\n                                        value: searchQuery,\n                                        onChange: function(e) {\n                                            return setSearchQuery(e.target.value);\n                                        },\n                                        className: \"w-48 lg:w-80 px-4 py-2 pl-10 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-search-line text-gray-400 text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"sm:hidden w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-search-line text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"IlI1Vc44qbqljMr4gj4jj4iJBBo=\");\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.js\n"));

/***/ }),

/***/ "./components/HeroCarousel.js":
/*!************************************!*\
  !*** ./components/HeroCarousel.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HeroCarousel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction HeroCarousel(param) {\n    var _featuredCards = param.featuredCards, featuredCards = _featuredCards === void 0 ? [] : _featuredCards;\n    var _this = this;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0), currentSlide = ref[0], setCurrentSlide = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), isHovered = ref1[0], setIsHovered = ref1[1];\n    // 如果没有传入featuredCards，使用默认数据\n    var defaultCarouselData = [\n        {\n            id: 1,\n            name: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"home.heroTitle1\"),\n            subtitle: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"home.heroSubtitle1\"),\n            images: [\n                \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=600&fit=crop\"\n            ],\n            price: 29999\n        },\n        {\n            id: 2,\n            name: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"home.heroTitle2\"),\n            subtitle: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"home.heroSubtitle2\"),\n            images: [\n                \"https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=1200&h=600&fit=crop\"\n            ],\n            price: 19999\n        },\n        {\n            id: 3,\n            name: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"home.heroTitle3\"),\n            subtitle: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"home.heroSubtitle3\"),\n            images: [\n                \"https://images.unsplash.com/photo-1574623452334-1e0ac2b3ccb4?w=1200&h=600&fit=crop\"\n            ],\n            price: 39999\n        }\n    ];\n    // 将featuredCards转换为轮播格式，或使用默认数据\n    var carouselData = featuredCards.length > 0 ? featuredCards.slice(0, 3).map(function(card) {\n        return {\n            id: card.id,\n            name: card.name,\n            subtitle: card.subtitle || (card.description ? card.description.substring(0, 50) + \"...\" : \"\"),\n            images: card.images || [],\n            price: card.price\n        };\n    }) : defaultCarouselData;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (!isHovered) {\n            var interval = setInterval(function() {\n                setCurrentSlide(function(prev) {\n                    return (prev + 1) % carouselData.length;\n                });\n            }, 4000);\n            return function() {\n                return clearInterval(interval);\n            };\n        }\n    }, [\n        isHovered\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-[600px] overflow-hidden bg-gradient-to-r from-blue-50 to-purple-50\",\n        onMouseEnter: function() {\n            return setIsHovered(true);\n        },\n        onMouseLeave: function() {\n            return setIsHovered(false);\n        },\n        children: [\n            carouselData.map(function(slide, index) {\n                var ref, ref1;\n                /*#__PURE__*/ return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 transition-all duration-1000 ease-in-out \".concat(index === currentSlide ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-full\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row items-center h-full px-4 sm:px-8 lg:px-16 py-8 lg:py-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 z-10 text-center lg:text-left mb-8 lg:mb-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-4 leading-tight\",\n                                        children: slide.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base sm:text-lg lg:text-xl text-gray-600 mb-6\",\n                                        children: slide.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center lg:justify-start space-x-4 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl sm:text-3xl font-bold text-blue-600\",\n                                                children: [\n                                                    \"RM\",\n                                                    (ref = slide.price) === null || ref === void 0 ? void 0 : ref.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, _this),\n                                            slide.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 line-through text-lg sm:text-xl\",\n                                                children: [\n                                                    \"RM\",\n                                                    (ref1 = slide.originalPrice) === null || ref1 === void 0 ? void 0 : ref1.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/card/\".concat(slide.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    className: \"bg-blue-600 text-white px-6 sm:px-8 py-3 rounded-full hover:bg-blue-700 transition-all transform hover:scale-105 whitespace-nowrap text-center\",\n                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"home.buyNow\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/card/\".concat(slide.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    className: \"border-2 border-gray-300 text-gray-700 px-6 sm:px-8 py-3 rounded-full hover:border-blue-600 hover:text-blue-600 transition-all whitespace-nowrap text-center\",\n                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"home.viewDetails\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative w-full lg:w-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/card/\".concat(slide.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"block relative w-full h-64 sm:h-80 lg:h-96 transform hover:scale-105 transition-transform duration-300 cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: function() {\n                                                    if (Array.isArray(slide.images) && slide.images.length > 0) {\n                                                        return slide.images[0];\n                                                    } else if (typeof slide.images === \"string\") {\n                                                        try {\n                                                            var parsedImages = JSON.parse(slide.images);\n                                                            return Array.isArray(parsedImages) && parsedImages.length > 0 ? parsedImages[0] : \"\";\n                                                        } catch (e) {\n                                                            return slide.images;\n                                                        }\n                                                    }\n                                                    return \"\";\n                                                }(),\n                                                alt: slide.name,\n                                                className: \"w-full h-full object-cover object-top rounded-2xl shadow-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, _this)\n                }, slide.id, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, _this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3\",\n                children: carouselData.map(function(_, index) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return setCurrentSlide(index);\n                        },\n                        className: \"w-3 h-3 rounded-full transition-all duration-300 \".concat(index === currentSlide ? \"bg-blue-600 scale-125\" : \"bg-white/50 hover:bg-white/75\")\n                    }, index, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\HeroCarousel.js\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(HeroCarousel, \"ihwtC0nD02yXo+6vkTKPCmd99yA=\");\n_c = HeroCarousel;\nvar _c;\n$RefreshReg$(_c, \"HeroCarousel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/HeroCarousel.js\n"));

/***/ }),

/***/ "./components/LanguageSwitcher.js":
/*!****************************************!*\
  !*** ./components/LanguageSwitcher.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LanguageSwitcher; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\nvar languages = [\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDEC\\uD83C\\uDDE7\"\n    },\n    {\n        code: \"zh\",\n        name: \"中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    {\n        code: \"ms\",\n        name: \"Bahasa Malaysia\",\n        flag: \"\\uD83C\\uDDF2\\uD83C\\uDDFE\"\n    }\n];\nfunction LanguageSwitcher() {\n    var _this = this;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\"), currentLang = ref[0], setCurrentLang = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), isOpen = ref1[0], setIsOpen = ref1[1];\n    var dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        // 获取当前语言\n        var lang = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getCurrentLanguage)();\n        setCurrentLang(lang);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var handleClickOutside = // 点击外部关闭下拉菜单\n        function handleClickOutside(event) {\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        };\n        if (isOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isOpen\n    ]);\n    var handleLanguageChange = function(langCode) {\n        (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.setLanguage)(langCode);\n        setCurrentLang(langCode);\n        setIsOpen(false);\n        // 刷新页面以应用新语言\n        window.location.reload();\n    };\n    var currentLanguage = languages.find(function(lang) {\n        return lang.code === currentLang;\n    }) || languages[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: function() {\n                    return setIsOpen(!isOpen);\n                },\n                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                \"aria-label\": \"Language Switcher\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: currentLanguage.flag\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hidden sm:block text-sm font-medium text-gray-700\",\n                        children: currentLanguage.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-arrow-down-s-line text-gray-400 transition-transform duration-200 \".concat(isOpen ? \"rotate-180\" : \"\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in\",\n                children: languages.map(function(language) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return handleLanguageChange(language.code);\n                        },\n                        className: \"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 \".concat(currentLang === language.code ? \"bg-blue-50 text-blue-600\" : \"text-gray-700\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg\",\n                                children: language.flag\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: language.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, _this),\n                            currentLang === language.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-check-line ml-auto text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                                lineNumber: 80,\n                                columnNumber: 17\n                            }, _this)\n                        ]\n                    }, language.code, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageSwitcher, \"YfS1rnW1CbleKHeOzAsHD3ydWrY=\");\n_c = LanguageSwitcher;\nvar _c;\n$RefreshReg$(_c, \"LanguageSwitcher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/LanguageSwitcher.js\n"));

/***/ }),

/***/ "./components/PriceDisplay.js":
/*!************************************!*\
  !*** ./components/PriceDisplay.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PriceDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction PriceDisplay(param) {\n    var price = param.price, originalPrice = param.originalPrice, estimatedValue = param.estimatedValue, _status = param.status, status = _status === void 0 ? null : _status, _showToggle = param.showToggle, showToggle = _showToggle === void 0 ? true : _showToggle, _defaultType = param.defaultType, defaultType = _defaultType === void 0 ? \"purchase\" : _defaultType, _currentType = param.currentType, currentType = _currentType === void 0 ? null // 外部传入的当前价格类型\n     : _currentType;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultType), priceType = ref[0], setPriceType = ref[1];\n    // 响应外部价格类型变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (currentType && currentType !== priceType) {\n            setPriceType(currentType);\n        }\n    }, [\n        currentType\n    ]);\n    // 格式化价格\n    var formatPrice = function(price) {\n        if (price === null || price === undefined || isNaN(price) || price === 0) return (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"common.loading\");\n        return \"RM\".concat(Number(price).toLocaleString());\n    };\n    // 获取显示价格\n    var getDisplayPrice = function() {\n        if (priceType === \"estimated\") {\n            return estimatedValue || price;\n        }\n        return price;\n    };\n    // 获取价格标签\n    var getPriceLabel = function() {\n        if (priceType === \"estimated\") {\n            return (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.estimatedValue\");\n        }\n        return status === (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.auctioning\") ? (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.price\") : (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.purchasePrice\");\n    };\n    // 计算价格差异\n    var getPriceDifference = function() {\n        if (!estimatedValue || !price) return null;\n        var difference = estimatedValue - price;\n        var percentage = (difference / price * 100).toFixed(1);\n        return {\n            amount: difference,\n            percentage: percentage,\n            isPositive: difference > 0\n        };\n    };\n    var priceDiff = getPriceDifference();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2 min-h-[4rem] flex flex-col\",\n        children: [\n            showToggle && estimatedValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex bg-gray-100 rounded-lg p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return setPriceType(\"purchase\");\n                        },\n                        className: \"flex-1 px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200 \".concat(priceType === \"purchase\" ? \"bg-white text-blue-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                        children: [\n                            (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"home.buyNowTab\"),\n                            \"价\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return setPriceType(\"estimated\");\n                        },\n                        className: \"flex-1 px-3 py-1.5 rounded-md text-xs font-medium transition-all duration-200 \".concat(priceType === \"estimated\" ? \"bg-white text-blue-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"home.valuationTab\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col justify-end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs mb-1 font-medium transition-colors duration-200 h-[1rem] flex items-center \".concat(priceType === \"estimated\" ? \"text-purple-600\" : \"text-blue-600\"),\n                        children: getPriceLabel()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-bold transition-all duration-300 \".concat(priceType === \"estimated\" ? \"text-purple-700\" : \"text-blue-700\"),\n                        children: formatPrice(getDisplayPrice())\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-[1.25rem] mt-1 flex items-center\",\n                        children: [\n                            priceType === \"purchase\" && originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500 line-through\",\n                                children: [\n                                    (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.originalPrice\"),\n                                    \": \",\n                                    formatPrice(originalPrice)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            priceType === \"estimated\" && price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.purchasePrice\"),\n                                    \": \",\n                                    formatPrice(price)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-[1rem] flex items-center\",\n                        children: [\n                            priceDiff && priceType === \"purchase\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs \".concat(priceDiff.isPositive ? \"text-green-600\" : \"text-red-600\"),\n                                children: [\n                                    priceDiff.isPositive ? \"↗\" : \"↘\",\n                                    \" \",\n                                    (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.valuation\"),\n                                    \" \",\n                                    formatPrice(Math.abs(priceDiff.amount)),\n                                    \"(\",\n                                    priceDiff.isPositive ? \"+\" : \"\",\n                                    priceDiff.percentage,\n                                    \"%)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            priceDiff && priceType === \"estimated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs \".concat(priceDiff.isPositive ? \"text-green-600\" : \"text-red-600\"),\n                                children: [\n                                    priceDiff.isPositive ? (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.higherThan\") : (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.lowerThan\"),\n                                    (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.purchasePrice\"),\n                                    \" \",\n                                    formatPrice(Math.abs(priceDiff.amount))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\PriceDisplay.js\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n_s(PriceDisplay, \"/FM6s8EtEpQVlqjok3P8JUkabYE=\");\n_c = PriceDisplay;\nvar _c;\n$RefreshReg$(_c, \"PriceDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PriceDisplay.js\n"));

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction Sidebar(param) {\n    var _categories = param.categories, categories = _categories === void 0 ? [] : _categories, onCategorySelect = param.onCategorySelect, selectedCategory = param.selectedCategory, _loading = param.loading, loading = _loading === void 0 ? false : _loading, _isMobile = param.isMobile, isMobile = _isMobile === void 0 ? false : _isMobile;\n    var _this = this;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedCategory || null), activeCategory = ref[0], setActiveCategory = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), clickedCategory = ref1[0], setClickedCategory = ref1[1];\n    // 只使用数据表中的分类数据\n    var displayCategories = categories;\n    var handleCategoryClick = function(categoryId) {\n        setActiveCategory(categoryId);\n        setClickedCategory(categoryId);\n        if (onCategorySelect) {\n            onCategorySelect(categoryId);\n        }\n        // 清除点击状态\n        setTimeout(function() {\n            setClickedCategory(null);\n        }, 300);\n    };\n    if (isMobile) {\n        // 手机端水平滚动布局\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-3 overflow-x-auto pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return handleCategoryClick(null);\n                        },\n                        className: \"flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap \".concat(activeCategory === null ? \"bg-blue-600 text-white shadow-lg\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"sidebar.allCoin\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    displayCategories.length > 0 ? displayCategories.map(function(category) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleCategoryClick(category.id);\n                            },\n                            className: \"flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap \".concat(activeCategory === category.id ? \"bg-blue-600 text-white shadow-lg\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getCategoryName)(category.name)\n                        }, category.id, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                            lineNumber: 42,\n                            columnNumber: 15\n                        }, _this);\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 px-4 py-2 text-sm text-gray-500\",\n                        children: \"No categories available\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                        lineNumber: 55,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    }\n    // 桌面端垂直布局\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-64 bg-white border-r border-gray-200 h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"sidebar.categories\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleCategoryClick(null);\n                            },\n                            disabled: loading,\n                            className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left group transform \".concat(clickedCategory === null ? \"scale-95\" : \"\", \" \").concat(activeCategory === null ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white\" : \"text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:text-blue-600\", \" \").concat(loading ? \"opacity-50 cursor-not-allowed\" : \"hover:scale-105\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 flex items-center justify-center \".concat(activeCategory === null ? \"text-white\" : \"text-gray-500 group-hover:text-blue-500\"),\n                                    children: loading && activeCategory === null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-grid-line\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1 whitespace-nowrap\",\n                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"sidebar.allCoin\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        displayCategories.length > 0 ? displayCategories.map(function(category) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: function() {\n                                    return handleCategoryClick(category.id);\n                                },\n                                disabled: loading,\n                                className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left group transform \".concat(clickedCategory === category.id ? \"scale-95\" : \"\", \" \").concat(activeCategory === category.id ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white\" : \"text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:text-blue-600\", \" \").concat(loading ? \"opacity-50 cursor-not-allowed\" : \"hover:scale-105\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-5 h-5 flex items-center justify-center \".concat(activeCategory === category.id ? \"text-white\" : \"text-gray-500 group-hover:text-blue-500\"),\n                                        children: loading && activeCategory === category.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 21\n                                        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: category.icon || \"ri-folder-line\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 114,\n                                            columnNumber: 21\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex-1 whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getCategoryName)(category.name)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm px-2 py-1 rounded-full \".concat(activeCategory === category.id ? \"bg-white/20 text-white\" : \"bg-gray-100 text-gray-500 group-hover:bg-blue-100 group-hover:text-blue-600\"),\n                                        children: category.count || 0\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, category.id, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, _this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full px-4 py-3 text-center text-gray-500 text-sm\",\n                            children: \"No categories available\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 p-4 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-fire-line text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-orange-700\",\n                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"home.hotRecommendation\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-orange-600\",\n                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"home.weeklyPopular\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Sidebar.js\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"VsDF6WaE23jK3WlHy6pAQINK/Vg=\");\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n"));

/***/ }),

/***/ "./hooks/useSiteConfig.js":
/*!********************************!*\
  !*** ./hooks/useSiteConfig.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"getCurrentSiteConfig\": function() { return /* binding */ getCurrentSiteConfig; },\n/* harmony export */   \"refreshSiteConfig\": function() { return /* binding */ refreshSiteConfig; },\n/* harmony export */   \"useSiteConfig\": function() { return /* binding */ useSiteConfig; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\n\nvar _s = $RefreshSig$();\n\n\n// 获取默认配置的函数\nvar getDefaultConfig = function() {\n    return {\n        title: \"myduitlama\",\n        description: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_1__.t)(\"about.subtitle\"),\n        logo: \"myduitlama\",\n        navigation: []\n    };\n};\n// 全局配置缓存\nvar globalConfig = null;\nvar configPromise = null;\nfunction useSiteConfig() {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(globalConfig || getDefaultConfig()), siteConfig = ref[0], setSiteConfig = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!globalConfig), loading = ref1[0], setLoading = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null), error = ref2[0], setError = ref2[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        // 如果已经有全局配置，直接使用\n        if (globalConfig) {\n            setSiteConfig(globalConfig);\n            setLoading(false);\n            return;\n        }\n        // 如果正在加载中，等待现有的Promise\n        if (configPromise) {\n            configPromise.then(function(config) {\n                setSiteConfig(config);\n                setLoading(false);\n            }).catch(function(err) {\n                setError(err);\n                setLoading(false);\n            });\n            return;\n        }\n        // 开始新的加载\n        setLoading(true);\n        configPromise = loadSiteConfig();\n        configPromise.then(function(config) {\n            globalConfig = config;\n            setSiteConfig(config);\n            setLoading(false);\n        }).catch(function(err) {\n            console.error(\"Error loading site config:\", err);\n            setError(err);\n            setLoading(false);\n            // 使用默认配置\n            setSiteConfig(getDefaultConfig());\n        }).finally(function() {\n            configPromise = null;\n        });\n    }, []);\n    return {\n        siteConfig: siteConfig,\n        loading: loading,\n        error: error\n    };\n}\n_s(useSiteConfig, \"iRrxyPqodM5BJIsTc13ZKlRI61w=\");\nfunction loadSiteConfig() {\n    return _loadSiteConfig.apply(this, arguments);\n}\nfunction _loadSiteConfig() {\n    _loadSiteConfig = // 加载网站配置的函数\n    (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var response, config, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    _state.trys.push([\n                        0,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        fetch(\"/api/site-config\")\n                    ];\n                case 1:\n                    response = _state.sent();\n                    if (!response.ok) {\n                        throw new Error(\"Failed to fetch site config\");\n                    }\n                    return [\n                        4,\n                        response.json()\n                    ];\n                case 2:\n                    config = _state.sent();\n                    return [\n                        2,\n                        config\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"Error fetching site config:\", error);\n                    return [\n                        2,\n                        getDefaultConfig()\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    });\n    return _loadSiteConfig.apply(this, arguments);\n}\n// 刷新配置的函数（用于后台更新后刷新）\nfunction refreshSiteConfig() {\n    globalConfig = null;\n    return loadSiteConfig().then(function(config) {\n        globalConfig = config;\n        return config;\n    });\n}\n// 获取当前配置的函数（同步）\nfunction getCurrentSiteConfig() {\n    return globalConfig || getDefaultConfig();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useSiteConfig.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getDomainLocale = getDomainLocale;\nvar basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) { var finalLocale, proto, domain, target, detectDomainLocale, normalizeLocalePath; } else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _slicedToArray = (__webpack_require__(/*! @swc/helpers/lib/_sliced_to_array.js */ \"./node_modules/@swc/helpers/lib/_sliced_to_array.js\")[\"default\"]);\nvar _typeOf = (__webpack_require__(/*! @swc/helpers/lib/_type_of.js */ \"./node_modules/@swc/helpers/lib/_type_of.js\")[\"default\"]);\nvar _s = $RefreshSig$();\n\"client\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = void 0;\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _object_without_properties_loose = (__webpack_require__(/*! @swc/helpers/lib/_object_without_properties_loose.js */ \"./node_modules/@swc/helpers/lib/_object_without_properties_loose.js\")[\"default\"]);\nvar _react = _interop_require_default(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nvar _router = __webpack_require__(/*! ../shared/lib/router/router */ \"./node_modules/next/dist/shared/lib/router/router.js\");\nvar _addLocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nvar _routerContext = __webpack_require__(/*! ../shared/lib/router-context */ \"./node_modules/next/dist/shared/lib/router-context.js\");\nvar _appRouterContext = __webpack_require__(/*! ../shared/lib/app-router-context */ \"./node_modules/next/dist/shared/lib/app-router-context.js\");\nvar _useIntersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nvar _getDomainLocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nvar _addBasePath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\n\"client\";\nvar prefetched = {};\nfunction prefetch(router, href, as, options) {\n    if ( false || !router) return;\n    if (!(0, _router).isLocalURL(href)) return;\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    Promise.resolve(router.prefetch(href, as, options)).catch(function(err) {\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n    var curLocale = options && typeof options.locale !== \"undefined\" ? options.locale : router && router.locale;\n    // Join on an invalid URI character\n    prefetched[href + \"%\" + as + (curLocale ? \"%\" + curLocale : \"\")] = true;\n}\nfunction isModifiedEvent(event) {\n    var target = event.currentTarget.target;\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled) {\n    var nodeName = e.currentTarget.nodeName;\n    // anchors inside an svg have a lowercase nodeName\n    var isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || !(0, _router).isLocalURL(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    var navigate = function() {\n        // If the router is an NextRouter instance it will have `beforePopState`\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow: shallow,\n                locale: locale,\n                scroll: scroll\n            });\n        } else {\n            // If `beforePopState` doesn't exist on the router it's the AppRouter.\n            var method = replace ? \"replace\" : \"push\";\n            router[method](href, {\n                forceOptimisticNavigation: !prefetchEnabled\n            });\n        }\n    };\n    if (isAppRouter) {\n        // @ts-expect-error startTransition exists.\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nvar Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    if (true) {\n        var createPropError = function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\".concat(args.key, \"` expects a \").concat(args.expected, \" in `<Link>`, but got `\").concat(args.actual, \"` instead.\") + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        };\n        // TypeScript trick for type-guarding:\n        var requiredPropsGuard = {\n            href: true\n        };\n        var requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach(function(key) {\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : _typeOf(props[key])\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                var _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        var optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        var optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach(function(key) {\n            var valType = _typeOf(props[key]);\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                var _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        var hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    var children;\n    var hrefProp = props.href, asProp = props.as, childrenProp = props.children, prefetchProp = props.prefetch, passHref = props.passHref, replace = props.replace, shallow = props.shallow, scroll = props.scroll, locale = props.locale, onClick = props.onClick, onMouseEnter = props.onMouseEnter, onTouchStart = props.onTouchStart, _legacyBehavior = props.legacyBehavior, legacyBehavior = _legacyBehavior === void 0 ? Boolean(false) !== true : _legacyBehavior, restProps = _object_without_properties_loose(props, [\n        \"href\",\n        \"as\",\n        \"children\",\n        \"prefetch\",\n        \"passHref\",\n        \"replace\",\n        \"shallow\",\n        \"scroll\",\n        \"locale\",\n        \"onClick\",\n        \"onMouseEnter\",\n        \"onTouchStart\",\n        \"legacyBehavior\"\n    ]);\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ _react.default.createElement(\"a\", null, children);\n    }\n    var p = prefetchProp !== false;\n    var router = _react.default.useContext(_routerContext.RouterContext);\n    // TODO-APP: type error. Remove `as any`\n    var appRouter = _react.default.useContext(_appRouterContext.AppRouterContext);\n    if (appRouter) {\n        router = appRouter;\n    }\n    var ref = _react.default.useMemo(function() {\n        var ref = _slicedToArray((0, _router).resolveHref(router, hrefProp, true), 2), resolvedHref = ref[0], resolvedAs = ref[1];\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _router).resolveHref(router, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        router,\n        hrefProp,\n        asProp\n    ]), href = ref.href, as = ref.as;\n    var previousHref = _react.default.useRef(href);\n    var previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    var child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `'.concat(hrefProp, '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link'));\n            }\n            if (onMouseEnter) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `'.concat(hrefProp, '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link'));\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\".concat(hrefProp, \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"));\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\".concat(hrefProp, \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\") + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    }\n    var childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    var ref1 = _slicedToArray((0, _useIntersection).useIntersection({\n        rootMargin: \"200px\"\n    }), 3), setIntersectionRef = ref1[0], isVisible = ref1[1], resetVisible = ref1[2];\n    var setRef = _react.default.useCallback(function(el) {\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    _react.default.useEffect(function() {\n        var shouldPrefetch = isVisible && p && (0, _router).isLocalURL(href);\n        var curLocale = typeof locale !== \"undefined\" ? locale : router && router.locale;\n        var isPrefetched = prefetched[href + \"%\" + as + (curLocale ? \"%\" + curLocale : \"\")];\n        if (shouldPrefetch && !isPrefetched) {\n            prefetch(router, href, as, {\n                locale: curLocale\n            });\n        }\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        p,\n        router\n    ]);\n    var childProps = {\n        ref: setRef,\n        onClick: function(e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!e.defaultPrevented) {\n                linkClicked(e, router, href, as, replace, shallow, scroll, locale, Boolean(appRouter), p);\n            }\n        },\n        onMouseEnter: function(e) {\n            if (!legacyBehavior && typeof onMouseEnter === \"function\") {\n                onMouseEnter(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            // Check for not prefetch disabled in page using appRouter\n            if (!(!p && appRouter)) {\n                if ((0, _router).isLocalURL(href)) {\n                    prefetch(router, href, as, {\n                        priority: true\n                    });\n                }\n            }\n        },\n        onTouchStart: function(e) {\n            if (!legacyBehavior && typeof onTouchStart === \"function\") {\n                onTouchStart(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            // Check for not prefetch disabled in page using appRouter\n            if (!(!p && appRouter)) {\n                if ((0, _router).isLocalURL(href)) {\n                    prefetch(router, href, as, {\n                        priority: true\n                    });\n                }\n            }\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user\n    if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        var curLocale = typeof locale !== \"undefined\" ? locale : router && router.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        var localeDomain = router && router.isLocaleDomain && (0, _getDomainLocale).getDomainLocale(as, curLocale, router.locales, router.domainLocales);\n        childProps.href = localeDomain || (0, _addBasePath).addBasePath((0, _addLocale).addLocale(as, curLocale, router && router.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ _react.default.createElement(\"a\", Object.assign({}, restProps, childProps), children);\n}, \"xuB00qEWT1T+jc4Svm2qUBtJuIg=\")), \"xuB00qEWT1T+jc4Svm2qUBtJuIg=\");\n_c1 = Link;\nvar _default = Link;\nexports[\"default\"] = _default;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _slicedToArray = (__webpack_require__(/*! @swc/helpers/lib/_sliced_to_array.js */ \"./node_modules/@swc/helpers/lib/_sliced_to_array.js\")[\"default\"]);\nvar _s = $RefreshSig$();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.useIntersection = useIntersection;\nvar _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nvar _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nvar hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nvar observers = new Map();\nvar idList = [];\nfunction createObserver(options) {\n    var id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    var existing = idList.find(function(obj) {\n        return obj.root === id.root && obj.margin === id.margin;\n    });\n    var instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    var elements = new Map();\n    var observer = new IntersectionObserver(function(entries) {\n        entries.forEach(function(entry) {\n            var callback = elements.get(entry.target);\n            var isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id: id,\n        observer: observer,\n        elements: elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    var ref = createObserver(options), id = ref.id, observer = ref.observer, elements = ref.elements;\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            var index = idList.findIndex(function(obj) {\n                return obj.root === id.root && obj.margin === id.margin;\n            });\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    var rootRef = param.rootRef, rootMargin = param.rootMargin, disabled = param.disabled;\n    _s();\n    var isDisabled = disabled || !hasIntersectionObserver;\n    var ref = _slicedToArray((0, _react).useState(false), 2), visible = ref[0], setVisible = ref[1];\n    var ref1 = _slicedToArray((0, _react).useState(null), 2), element = ref1[0], setElement = ref1[1];\n    (0, _react).useEffect(function() {\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            if (element && element.tagName) {\n                var unobserve = observe(element, function(isVisible) {\n                    return isVisible && setVisible(isVisible);\n                }, {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin: rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                var idleCallback = (0, _requestIdleCallback).requestIdleCallback(function() {\n                    return setVisible(true);\n                });\n                return function() {\n                    return (0, _requestIdleCallback).cancelIdleCallback(idleCallback);\n                };\n            }\n        }\n    }, [\n        element,\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible\n    ]);\n    var resetVisible = (0, _react).useCallback(function() {\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\n_s(useIntersection, \"mCSdCffdW7h1A87zcVCmaEd/d2A=\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC91c2UtaW50ZXJzZWN0aW9uLmpzLmpzIiwibWFwcGluZ3MiOiJBQUFhOzs7Ozs7QUFDYkEsOENBQTZDO0lBQ3pDRyxLQUFLLEVBQUUsSUFBSTtDQUNkLEVBQUMsQ0FBQztBQUNIRCx1QkFBdUIsR0FBR0UsZUFBZSxDQUFDO0FBQzFDLElBQUlDLE1BQU0sR0FBR0MsbUJBQU8sQ0FBQyw0Q0FBTyxDQUFDO0FBQzdCLElBQUlDLG9CQUFvQixHQUFHRCxtQkFBTyxDQUFDLHlGQUF5QixDQUFDO0FBQzdELElBQU1FLHVCQUF1QixHQUFHLE9BQU9DLG9CQUFvQixLQUFLLFVBQVU7QUFDMUUsSUFBTUMsU0FBUyxHQUFHLElBQUlDLEdBQUcsRUFBRTtBQUMzQixJQUFNQyxNQUFNLEdBQUcsRUFBRTtBQUNqQixTQUFTQyxjQUFjLENBQUNDLE9BQU8sRUFBRTtJQUM3QixJQUFNQyxFQUFFLEdBQUc7UUFDUEMsSUFBSSxFQUFFRixPQUFPLENBQUNFLElBQUksSUFBSSxJQUFJO1FBQzFCQyxNQUFNLEVBQUVILE9BQU8sQ0FBQ0ksVUFBVSxJQUFJLEVBQUU7S0FDbkM7SUFDRCxJQUFNQyxRQUFRLEdBQUdQLE1BQU0sQ0FBQ1EsSUFBSSxDQUFDLFNBQUNDLEdBQUc7ZUFBR0EsR0FBRyxDQUFDTCxJQUFJLEtBQUtELEVBQUUsQ0FBQ0MsSUFBSSxJQUFJSyxHQUFHLENBQUNKLE1BQU0sS0FBS0YsRUFBRSxDQUFDRSxNQUFNO0tBQUEsQ0FBQztJQUNyRixJQUFJSyxRQUFRO0lBQ1osSUFBSUgsUUFBUSxFQUFFO1FBQ1ZHLFFBQVEsR0FBR1osU0FBUyxDQUFDYSxHQUFHLENBQUNKLFFBQVEsQ0FBQyxDQUFDO1FBQ25DLElBQUlHLFFBQVEsRUFBRTtZQUNWLE9BQU9BLFFBQVEsQ0FBQztRQUNwQixDQUFDO0lBQ0wsQ0FBQztJQUNELElBQU1FLFFBQVEsR0FBRyxJQUFJYixHQUFHLEVBQUU7SUFDMUIsSUFBTWMsUUFBUSxHQUFHLElBQUloQixvQkFBb0IsQ0FBQyxTQUFDaUIsT0FBTyxFQUFHO1FBQ2pEQSxPQUFPLENBQUNDLE9BQU8sQ0FBQyxTQUFDQyxLQUFLLEVBQUc7WUFDckIsSUFBTUMsUUFBUSxHQUFHTCxRQUFRLENBQUNELEdBQUcsQ0FBQ0ssS0FBSyxDQUFDRSxNQUFNLENBQUM7WUFDM0MsSUFBTUMsU0FBUyxHQUFHSCxLQUFLLENBQUNJLGNBQWMsSUFBSUosS0FBSyxDQUFDSyxpQkFBaUIsR0FBRyxDQUFDO1lBQ3JFLElBQUlKLFFBQVEsSUFBSUUsU0FBUyxFQUFFO2dCQUN2QkYsUUFBUSxDQUFDRSxTQUFTLENBQUMsQ0FBQztZQUN4QixDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDLEVBQUVqQixPQUFPLENBQUM7SUFDWFEsUUFBUSxHQUFHO1FBQ1BQLEVBQUUsRUFBRkEsRUFBRTtRQUNGVSxRQUFRLEVBQVJBLFFBQVE7UUFDUkQsUUFBUSxFQUFSQSxRQUFRO0tBQ1gsQ0FBQztJQUNGWixNQUFNLENBQUNzQixJQUFJLENBQUNuQixFQUFFLENBQUMsQ0FBQztJQUNoQkwsU0FBUyxDQUFDeUIsR0FBRyxDQUFDcEIsRUFBRSxFQUFFTyxRQUFRLENBQUMsQ0FBQztJQUM1QixPQUFPQSxRQUFRLENBQUM7QUFDcEIsQ0FBQztBQUNELFNBQVNjLE9BQU8sQ0FBQ0MsT0FBTyxFQUFFUixRQUFRLEVBQUVmLE9BQU8sRUFBRTtJQUN6QyxJQUFzQ0QsR0FBdUIsR0FBdkJBLGNBQWMsQ0FBQ0MsT0FBTyxDQUFDLEVBQXJEQyxFQUFFLEdBQTRCRixHQUF1QixDQUFyREUsRUFBRSxFQUFHVSxRQUFRLEdBQWlCWixHQUF1QixDQUFoRFksUUFBUSxFQUFHRCxRQUFRLEdBQU1YLEdBQXVCLENBQXJDVyxRQUFRO0lBQ2hDQSxRQUFRLENBQUNXLEdBQUcsQ0FBQ0UsT0FBTyxFQUFFUixRQUFRLENBQUMsQ0FBQztJQUNoQ0osUUFBUSxDQUFDVyxPQUFPLENBQUNDLE9BQU8sQ0FBQyxDQUFDO0lBQzFCLE9BQU8sU0FBU0MsU0FBUyxHQUFHO1FBQ3hCZCxRQUFRLENBQUNlLE1BQU0sQ0FBQ0YsT0FBTyxDQUFDLENBQUM7UUFDekJaLFFBQVEsQ0FBQ2EsU0FBUyxDQUFDRCxPQUFPLENBQUMsQ0FBQztRQUM1Qix1REFBdUQ7UUFDdkQsSUFBSWIsUUFBUSxDQUFDZ0IsSUFBSSxLQUFLLENBQUMsRUFBRTtZQUNyQmYsUUFBUSxDQUFDZ0IsVUFBVSxFQUFFLENBQUM7WUFDdEIvQixTQUFTLENBQUM2QixNQUFNLENBQUN4QixFQUFFLENBQUMsQ0FBQztZQUNyQixJQUFNMkIsS0FBSyxHQUFHOUIsTUFBTSxDQUFDK0IsU0FBUyxDQUFDLFNBQUN0QixHQUFHO3VCQUFHQSxHQUFHLENBQUNMLElBQUksS0FBS0QsRUFBRSxDQUFDQyxJQUFJLElBQUlLLEdBQUcsQ0FBQ0osTUFBTSxLQUFLRixFQUFFLENBQUNFLE1BQU07YUFBQSxDQUFDO1lBQ3ZGLElBQUl5QixLQUFLLEdBQUcsQ0FBQyxDQUFDLEVBQUU7Z0JBQ1o5QixNQUFNLENBQUNnQyxNQUFNLENBQUNGLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQztZQUM1QixDQUFDO1FBQ0wsQ0FBQztJQUNMLENBQUMsQ0FBQztBQUNOLENBQUM7QUFDRCxTQUFTdEMsZUFBZSxDQUFDLEtBQW9DLEVBQUU7UUFBcEN5QyxPQUFPLEdBQVQsS0FBb0MsQ0FBbENBLE9BQU8sRUFBRzNCLFVBQVUsR0FBdEIsS0FBb0MsQ0FBeEJBLFVBQVUsRUFBRzRCLFFBQVEsR0FBakMsS0FBb0MsQ0FBWEEsUUFBUTs7SUFDdEQsSUFBTUMsVUFBVSxHQUFHRCxRQUFRLElBQUksQ0FBQ3RDLHVCQUF1QjtJQUN2RCxJQUE4QixHQUEyQixrQkFBM0IsQ0FBQyxDQUFDLEVBQUVILE1BQU0sRUFBRTJDLFFBQVEsQ0FBQyxLQUFLLENBQUMsTUFBbERDLE9BQU8sR0FBZ0IsR0FBMkIsR0FBM0MsRUFBRUMsVUFBVSxHQUFJLEdBQTJCLEdBQS9CO0lBQzFCLElBQThCLElBQTBCLGtCQUExQixDQUFDLENBQUMsRUFBRTdDLE1BQU0sRUFBRTJDLFFBQVEsQ0FBQyxJQUFJLENBQUMsTUFBakRYLE9BQU8sR0FBZ0IsSUFBMEIsR0FBMUMsRUFBRWMsVUFBVSxHQUFJLElBQTBCLEdBQTlCO0lBQ3pCLEVBQUMsRUFBRTlDLE1BQU0sRUFBRStDLFNBQVMsQ0FBQyxXQUFJO1FBQ3RCLElBQUk1Qyx1QkFBdUIsRUFBRTtZQUN6QixJQUFJdUMsVUFBVSxJQUFJRSxPQUFPLEVBQUUsT0FBTztZQUNsQyxJQUFJWixPQUFPLElBQUlBLE9BQU8sQ0FBQ2dCLE9BQU8sRUFBRTtnQkFDNUIsSUFBTWYsU0FBUyxHQUFHRixPQUFPLENBQUNDLE9BQU8sRUFBRSxTQUFDTixTQUFTOzJCQUFHQSxTQUFTLElBQUltQixVQUFVLENBQUNuQixTQUFTLENBQUM7aUJBQUEsRUFBRTtvQkFDaEZmLElBQUksRUFBRTZCLE9BQU8sSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdBLE9BQU8sQ0FBQ1MsT0FBTztvQkFDaERwQyxVQUFVLEVBQVZBLFVBQVU7aUJBQ2IsQ0FBQztnQkFDRixPQUFPb0IsU0FBUyxDQUFDO1lBQ3JCLENBQUM7UUFDTCxPQUFPO1lBQ0gsSUFBSSxDQUFDVyxPQUFPLEVBQUU7Z0JBQ1YsSUFBTU0sWUFBWSxHQUFHLENBQUMsQ0FBQyxFQUFFaEQsb0JBQW9CLEVBQUVpRCxtQkFBbUIsQ0FBQzsyQkFBSU4sVUFBVSxDQUFDLElBQUksQ0FBQztpQkFBQSxDQUFDO2dCQUN4RixPQUFPOzJCQUFJLENBQUMsQ0FBQyxFQUFFM0Msb0JBQW9CLEVBQUVrRCxrQkFBa0IsQ0FBQ0YsWUFBWSxDQUFDO2lCQUFBLENBQUM7WUFDMUUsQ0FBQztRQUNMLENBQUM7SUFDTCxDQUFDLEVBQUU7UUFDQ2xCLE9BQU87UUFDUFUsVUFBVTtRQUNWN0IsVUFBVTtRQUNWMkIsT0FBTztRQUNQSSxPQUFPO0tBQ1YsQ0FBQyxDQUFDO0lBQ0gsSUFBTVMsWUFBWSxHQUFHLENBQUMsQ0FBQyxFQUFFckQsTUFBTSxFQUFFc0QsV0FBVyxDQUFDLFdBQUk7UUFDN0NULFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUN0QixDQUFDLEVBQUUsRUFBRSxDQUFDO0lBQ04sT0FBTztRQUNIQyxVQUFVO1FBQ1ZGLE9BQU87UUFDUFMsWUFBWTtLQUNmLENBQUM7QUFDTixDQUFDO0dBbkNRdEQsZUFBZTtBQXFDeEIsSUFBSSxDQUFDLE9BQU9GLE9BQU8sQ0FBQzBELE9BQU8sS0FBSyxVQUFVLElBQUssT0FBTzFELE9BQU8sQ0FBQzBELE9BQU8sS0FBSyxRQUFRLElBQUkxRCxPQUFPLENBQUMwRCxPQUFPLEtBQUssSUFBSSxDQUFDLElBQUssT0FBTzFELE9BQU8sQ0FBQzBELE9BQU8sQ0FBQ0MsVUFBVSxLQUFLLFdBQVcsRUFBRTtJQUNySzdELE1BQU0sQ0FBQ0MsY0FBYyxDQUFDQyxPQUFPLENBQUMwRCxPQUFPLEVBQUUsWUFBWSxFQUFFO1FBQUV6RCxLQUFLLEVBQUUsSUFBSTtLQUFFLENBQUMsQ0FBQztJQUN0RUgsTUFBTSxDQUFDOEQsTUFBTSxDQUFDNUQsT0FBTyxDQUFDMEQsT0FBTyxFQUFFMUQsT0FBTyxDQUFDLENBQUM7SUFDeEM2RCxNQUFNLENBQUM3RCxPQUFPLEdBQUdBLE9BQU8sQ0FBQzBELE9BQU8sQ0FBQztBQUNuQyxDQUFDLENBRUQsNENBQTRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L3VzZS1pbnRlcnNlY3Rpb24uanM/ZmQ5NCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMudXNlSW50ZXJzZWN0aW9uID0gdXNlSW50ZXJzZWN0aW9uO1xudmFyIF9yZWFjdCA9IHJlcXVpcmUoXCJyZWFjdFwiKTtcbnZhciBfcmVxdWVzdElkbGVDYWxsYmFjayA9IHJlcXVpcmUoXCIuL3JlcXVlc3QtaWRsZS1jYWxsYmFja1wiKTtcbmNvbnN0IGhhc0ludGVyc2VjdGlvbk9ic2VydmVyID0gdHlwZW9mIEludGVyc2VjdGlvbk9ic2VydmVyID09PSAnZnVuY3Rpb24nO1xuY29uc3Qgb2JzZXJ2ZXJzID0gbmV3IE1hcCgpO1xuY29uc3QgaWRMaXN0ID0gW107XG5mdW5jdGlvbiBjcmVhdGVPYnNlcnZlcihvcHRpb25zKSB7XG4gICAgY29uc3QgaWQgPSB7XG4gICAgICAgIHJvb3Q6IG9wdGlvbnMucm9vdCB8fCBudWxsLFxuICAgICAgICBtYXJnaW46IG9wdGlvbnMucm9vdE1hcmdpbiB8fCAnJ1xuICAgIH07XG4gICAgY29uc3QgZXhpc3RpbmcgPSBpZExpc3QuZmluZCgob2JqKT0+b2JqLnJvb3QgPT09IGlkLnJvb3QgJiYgb2JqLm1hcmdpbiA9PT0gaWQubWFyZ2luKTtcbiAgICBsZXQgaW5zdGFuY2U7XG4gICAgaWYgKGV4aXN0aW5nKSB7XG4gICAgICAgIGluc3RhbmNlID0gb2JzZXJ2ZXJzLmdldChleGlzdGluZyk7XG4gICAgICAgIGlmIChpbnN0YW5jZSkge1xuICAgICAgICAgICAgcmV0dXJuIGluc3RhbmNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0IGVsZW1lbnRzID0gbmV3IE1hcCgpO1xuICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IEludGVyc2VjdGlvbk9ic2VydmVyKChlbnRyaWVzKT0+e1xuICAgICAgICBlbnRyaWVzLmZvckVhY2goKGVudHJ5KT0+e1xuICAgICAgICAgICAgY29uc3QgY2FsbGJhY2sgPSBlbGVtZW50cy5nZXQoZW50cnkudGFyZ2V0KTtcbiAgICAgICAgICAgIGNvbnN0IGlzVmlzaWJsZSA9IGVudHJ5LmlzSW50ZXJzZWN0aW5nIHx8IGVudHJ5LmludGVyc2VjdGlvblJhdGlvID4gMDtcbiAgICAgICAgICAgIGlmIChjYWxsYmFjayAmJiBpc1Zpc2libGUpIHtcbiAgICAgICAgICAgICAgICBjYWxsYmFjayhpc1Zpc2libGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9LCBvcHRpb25zKTtcbiAgICBpbnN0YW5jZSA9IHtcbiAgICAgICAgaWQsXG4gICAgICAgIG9ic2VydmVyLFxuICAgICAgICBlbGVtZW50c1xuICAgIH07XG4gICAgaWRMaXN0LnB1c2goaWQpO1xuICAgIG9ic2VydmVycy5zZXQoaWQsIGluc3RhbmNlKTtcbiAgICByZXR1cm4gaW5zdGFuY2U7XG59XG5mdW5jdGlvbiBvYnNlcnZlKGVsZW1lbnQsIGNhbGxiYWNrLCBvcHRpb25zKSB7XG4gICAgY29uc3QgeyBpZCAsIG9ic2VydmVyICwgZWxlbWVudHMgIH0gPSBjcmVhdGVPYnNlcnZlcihvcHRpb25zKTtcbiAgICBlbGVtZW50cy5zZXQoZWxlbWVudCwgY2FsbGJhY2spO1xuICAgIG9ic2VydmVyLm9ic2VydmUoZWxlbWVudCk7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIHVub2JzZXJ2ZSgpIHtcbiAgICAgICAgZWxlbWVudHMuZGVsZXRlKGVsZW1lbnQpO1xuICAgICAgICBvYnNlcnZlci51bm9ic2VydmUoZWxlbWVudCk7XG4gICAgICAgIC8vIERlc3Ryb3kgb2JzZXJ2ZXIgd2hlbiB0aGVyZSdzIG5vdGhpbmcgbGVmdCB0byB3YXRjaDpcbiAgICAgICAgaWYgKGVsZW1lbnRzLnNpemUgPT09IDApIHtcbiAgICAgICAgICAgIG9ic2VydmVyLmRpc2Nvbm5lY3QoKTtcbiAgICAgICAgICAgIG9ic2VydmVycy5kZWxldGUoaWQpO1xuICAgICAgICAgICAgY29uc3QgaW5kZXggPSBpZExpc3QuZmluZEluZGV4KChvYmopPT5vYmoucm9vdCA9PT0gaWQucm9vdCAmJiBvYmoubWFyZ2luID09PSBpZC5tYXJnaW4pO1xuICAgICAgICAgICAgaWYgKGluZGV4ID4gLTEpIHtcbiAgICAgICAgICAgICAgICBpZExpc3Quc3BsaWNlKGluZGV4LCAxKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH07XG59XG5mdW5jdGlvbiB1c2VJbnRlcnNlY3Rpb24oeyByb290UmVmICwgcm9vdE1hcmdpbiAsIGRpc2FibGVkICB9KSB7XG4gICAgY29uc3QgaXNEaXNhYmxlZCA9IGRpc2FibGVkIHx8ICFoYXNJbnRlcnNlY3Rpb25PYnNlcnZlcjtcbiAgICBjb25zdCBbdmlzaWJsZSwgc2V0VmlzaWJsZV0gPSAoMCwgX3JlYWN0KS51c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW2VsZW1lbnQsIHNldEVsZW1lbnRdID0gKDAsIF9yZWFjdCkudXNlU3RhdGUobnVsbCk7XG4gICAgKDAsIF9yZWFjdCkudXNlRWZmZWN0KCgpPT57XG4gICAgICAgIGlmIChoYXNJbnRlcnNlY3Rpb25PYnNlcnZlcikge1xuICAgICAgICAgICAgaWYgKGlzRGlzYWJsZWQgfHwgdmlzaWJsZSkgcmV0dXJuO1xuICAgICAgICAgICAgaWYgKGVsZW1lbnQgJiYgZWxlbWVudC50YWdOYW1lKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgdW5vYnNlcnZlID0gb2JzZXJ2ZShlbGVtZW50LCAoaXNWaXNpYmxlKT0+aXNWaXNpYmxlICYmIHNldFZpc2libGUoaXNWaXNpYmxlKSwge1xuICAgICAgICAgICAgICAgICAgICByb290OiByb290UmVmID09IG51bGwgPyB2b2lkIDAgOiByb290UmVmLmN1cnJlbnQsXG4gICAgICAgICAgICAgICAgICAgIHJvb3RNYXJnaW5cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gdW5vYnNlcnZlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgaWYgKCF2aXNpYmxlKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgaWRsZUNhbGxiYWNrID0gKDAsIF9yZXF1ZXN0SWRsZUNhbGxiYWNrKS5yZXF1ZXN0SWRsZUNhbGxiYWNrKCgpPT5zZXRWaXNpYmxlKHRydWUpKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gKCk9PigwLCBfcmVxdWVzdElkbGVDYWxsYmFjaykuY2FuY2VsSWRsZUNhbGxiYWNrKGlkbGVDYWxsYmFjayk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LCBbXG4gICAgICAgIGVsZW1lbnQsXG4gICAgICAgIGlzRGlzYWJsZWQsXG4gICAgICAgIHJvb3RNYXJnaW4sXG4gICAgICAgIHJvb3RSZWYsXG4gICAgICAgIHZpc2libGVcbiAgICBdKTtcbiAgICBjb25zdCByZXNldFZpc2libGUgPSAoMCwgX3JlYWN0KS51c2VDYWxsYmFjaygoKT0+e1xuICAgICAgICBzZXRWaXNpYmxlKGZhbHNlKTtcbiAgICB9LCBbXSk7XG4gICAgcmV0dXJuIFtcbiAgICAgICAgc2V0RWxlbWVudCxcbiAgICAgICAgdmlzaWJsZSxcbiAgICAgICAgcmVzZXRWaXNpYmxlXG4gICAgXTtcbn1cblxuaWYgKCh0eXBlb2YgZXhwb3J0cy5kZWZhdWx0ID09PSAnZnVuY3Rpb24nIHx8ICh0eXBlb2YgZXhwb3J0cy5kZWZhdWx0ID09PSAnb2JqZWN0JyAmJiBleHBvcnRzLmRlZmF1bHQgIT09IG51bGwpKSAmJiB0eXBlb2YgZXhwb3J0cy5kZWZhdWx0Ll9fZXNNb2R1bGUgPT09ICd1bmRlZmluZWQnKSB7XG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLmRlZmF1bHQsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcbiAgT2JqZWN0LmFzc2lnbihleHBvcnRzLmRlZmF1bHQsIGV4cG9ydHMpO1xuICBtb2R1bGUuZXhwb3J0cyA9IGV4cG9ydHMuZGVmYXVsdDtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlLWludGVyc2VjdGlvbi5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJ1c2VJbnRlcnNlY3Rpb24iLCJfcmVhY3QiLCJyZXF1aXJlIiwiX3JlcXVlc3RJZGxlQ2FsbGJhY2siLCJoYXNJbnRlcnNlY3Rpb25PYnNlcnZlciIsIkludGVyc2VjdGlvbk9ic2VydmVyIiwib2JzZXJ2ZXJzIiwiTWFwIiwiaWRMaXN0IiwiY3JlYXRlT2JzZXJ2ZXIiLCJvcHRpb25zIiwiaWQiLCJyb290IiwibWFyZ2luIiwicm9vdE1hcmdpbiIsImV4aXN0aW5nIiwiZmluZCIsIm9iaiIsImluc3RhbmNlIiwiZ2V0IiwiZWxlbWVudHMiLCJvYnNlcnZlciIsImVudHJpZXMiLCJmb3JFYWNoIiwiZW50cnkiLCJjYWxsYmFjayIsInRhcmdldCIsImlzVmlzaWJsZSIsImlzSW50ZXJzZWN0aW5nIiwiaW50ZXJzZWN0aW9uUmF0aW8iLCJwdXNoIiwic2V0Iiwib2JzZXJ2ZSIsImVsZW1lbnQiLCJ1bm9ic2VydmUiLCJkZWxldGUiLCJzaXplIiwiZGlzY29ubmVjdCIsImluZGV4IiwiZmluZEluZGV4Iiwic3BsaWNlIiwicm9vdFJlZiIsImRpc2FibGVkIiwiaXNEaXNhYmxlZCIsInVzZVN0YXRlIiwidmlzaWJsZSIsInNldFZpc2libGUiLCJzZXRFbGVtZW50IiwidXNlRWZmZWN0IiwidGFnTmFtZSIsImN1cnJlbnQiLCJpZGxlQ2FsbGJhY2siLCJyZXF1ZXN0SWRsZUNhbGxiYWNrIiwiY2FuY2VsSWRsZUNhbGxiYWNrIiwicmVzZXRWaXNpYmxlIiwidXNlQ2FsbGJhY2siLCJkZWZhdWx0IiwiX19lc01vZHVsZSIsImFzc2lnbiIsIm1vZHVsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/app-router-context.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-router-context.js ***!
  \*****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.TemplateContext = exports.GlobalLayoutRouterContext = exports.LayoutRouterContext = exports.AppRouterContext = void 0;\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _react = _interop_require_default(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nvar AppRouterContext = _react.default.createContext(null);\nexports.AppRouterContext = AppRouterContext;\nvar LayoutRouterContext = _react.default.createContext(null);\nexports.LayoutRouterContext = LayoutRouterContext;\nvar GlobalLayoutRouterContext = _react.default.createContext(null);\nexports.GlobalLayoutRouterContext = GlobalLayoutRouterContext;\nvar TemplateContext = _react.default.createContext(null);\nexports.TemplateContext = TemplateContext;\nif (true) {\n    AppRouterContext.displayName = \"AppRouterContext\";\n    LayoutRouterContext.displayName = \"LayoutRouterContext\";\n    GlobalLayoutRouterContext.displayName = \"GlobalLayoutRouterContext\";\n    TemplateContext.displayName = \"TemplateContext\";\n} //# sourceMappingURL=app-router-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/app-router-context.js\n"));

/***/ }),

/***/ "./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Header */ \"./components/Header.js\");\n/* harmony import */ var _components_HeroCarousel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/HeroCarousel */ \"./components/HeroCarousel.js\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var _components_CardGrid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/CardGrid */ \"./components/CardGrid.js\");\n/* harmony import */ var _hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/useSiteConfig */ \"./hooks/useSiteConfig.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    var ref = (0,_hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_7__.useSiteConfig)(), siteConfig = ref.siteConfig, configLoading = ref.loading;\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), categories = ref1[0], setCategories = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), featuredCards = ref2[0], setFeaturedCards = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), loading = ref3[0], setLoading = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), selectedCategory = ref4[0], setSelectedCategory = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), allCards = ref5[0], setAllCards = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), categoryLoading = ref6[0], setCategoryLoading = ref6[1];\n    // 加载卡牌数据的函数\n    var loadCards = function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            var categoryId, showCategoryLoading, isMountedRef, minLoadingTime, startTime, url, response, data, elapsedTime, error;\n            var _arguments = arguments;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        categoryId = _arguments.length > 0 && _arguments[0] !== void 0 ? _arguments[0] : null, showCategoryLoading = _arguments.length > 1 && _arguments[1] !== void 0 ? _arguments[1] : false, isMountedRef = _arguments.length > 2 && _arguments[2] !== void 0 ? _arguments[2] : {\n                            current: true\n                        };\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            7,\n                            8,\n                            9\n                        ]);\n                        if (showCategoryLoading && isMountedRef.current) {\n                            setCategoryLoading(true);\n                        }\n                        minLoadingTime = showCategoryLoading ? 500 : 0;\n                        startTime = Date.now();\n                        url = new URL(\"/api/cards\", window.location.origin);\n                        url.searchParams.set(\"page\", \"1\");\n                        url.searchParams.set(\"limit\", \"12\");\n                        if (categoryId) {\n                            url.searchParams.set(\"category\", String(categoryId));\n                        }\n                        return [\n                            4,\n                            fetch(url.toString())\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        if (!(response.ok && isMountedRef.current)) return [\n                            3,\n                            6\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 3:\n                        data = _state.sent();\n                        elapsedTime = Date.now() - startTime;\n                        if (!(elapsedTime < minLoadingTime)) return [\n                            3,\n                            5\n                        ];\n                        return [\n                            4,\n                            new Promise(function(resolve) {\n                                return setTimeout(resolve, minLoadingTime - elapsedTime);\n                            })\n                        ];\n                    case 4:\n                        _state.sent();\n                        _state.label = 5;\n                    case 5:\n                        if (isMountedRef.current) {\n                            if (data.cards) {\n                                setAllCards(data.cards);\n                                setFeaturedCards(data.cards);\n                            } else {\n                                // 兼容旧格式\n                                setAllCards(data);\n                                setFeaturedCards(data);\n                            }\n                        }\n                        _state.label = 6;\n                    case 6:\n                        return [\n                            3,\n                            9\n                        ];\n                    case 7:\n                        error = _state.sent();\n                        if (isMountedRef.current) {\n                            console.error(\"Error loading cards:\", error);\n                        }\n                        return [\n                            3,\n                            9\n                        ];\n                    case 8:\n                        if (showCategoryLoading && isMountedRef.current) {\n                            setCategoryLoading(false);\n                        }\n                        return [\n                            7\n                        ];\n                    case 9:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function loadCards() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    // 处理分类选择\n    var handleCategorySelect = function(categoryId) {\n        setSelectedCategory(categoryId);\n        loadCards(categoryId, true); // 显示分类加载效果\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var isMountedRef = {\n            current: true\n        }; // 使用ref对象以便传递给异步函数\n        function loadData() {\n            return _loadData.apply(this, arguments);\n        }\n        function _loadData() {\n            _loadData = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n                var categoriesResponse, categoriesData, error;\n                return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            _state.trys.push([\n                                0,\n                                6,\n                                7,\n                                8\n                            ]);\n                            return [\n                                4,\n                                fetch(\"/api/categories\")\n                            ];\n                        case 1:\n                            categoriesResponse = _state.sent();\n                            if (!(categoriesResponse.ok && isMountedRef.current)) return [\n                                3,\n                                3\n                            ];\n                            return [\n                                4,\n                                categoriesResponse.json()\n                            ];\n                        case 2:\n                            categoriesData = _state.sent();\n                            setCategories(categoriesData);\n                            _state.label = 3;\n                        case 3:\n                            if (!isMountedRef.current) return [\n                                3,\n                                5\n                            ];\n                            return [\n                                4,\n                                loadCards(null, false, isMountedRef)\n                            ];\n                        case 4:\n                            _state.sent();\n                            _state.label = 5;\n                        case 5:\n                            return [\n                                3,\n                                8\n                            ];\n                        case 6:\n                            error = _state.sent();\n                            if (isMountedRef.current) {\n                                console.error(\"Error loading data:\", error);\n                            }\n                            return [\n                                3,\n                                8\n                            ];\n                        case 7:\n                            if (isMountedRef.current) {\n                                setLoading(false);\n                            }\n                            return [\n                                7\n                            ];\n                        case 8:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return _loadData.apply(this, arguments);\n        }\n        loadData();\n        // 清理函数\n        return function() {\n            isMountedRef.current = false;\n        };\n    }, []);\n    if (loading || configLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            \"Loading... - \",\n                            siteConfig.title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-white flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Loading...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: siteConfig.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: siteConfig.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        siteConfig: siteConfig,\n                        currentPage: \"home\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeroCarousel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                featuredCards: featuredCards\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                categories: categories,\n                                                onCategorySelect: handleCategorySelect,\n                                                selectedCategory: selectedCategory,\n                                                loading: categoryLoading,\n                                                isMobile: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            categories: categories,\n                                            onCategorySelect: handleCategorySelect,\n                                            selectedCategory: selectedCategory,\n                                            loading: categoryLoading,\n                                            isMobile: false\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CardGrid__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            cards: featuredCards,\n                                            enableInfiniteScroll: true,\n                                            categoryId: selectedCategory,\n                                            categoryLoading: categoryLoading\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\index.js\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"8QyIpTczLbb/a7oTkR4d9SaSDms=\", false, function() {\n    return [\n        _hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_7__.useSiteConfig\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.js\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ }),

/***/ "./node_modules/@swc/helpers/src/_array_like_to_array.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@swc/helpers/src/_array_like_to_array.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _arrayLikeToArray; }\n/* harmony export */ });\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9fYXJyYXlfbGlrZV90b19hcnJheS5tanMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQSx5Q0FBeUMsU0FBUztBQUNsRDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvc3JjL19hcnJheV9saWtlX3RvX2FycmF5Lm1qcz9kMzVjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIF9hcnJheUxpa2VUb0FycmF5KGFyciwgbGVuKSB7XG4gIGlmIChsZW4gPT0gbnVsbCB8fCBsZW4gPiBhcnIubGVuZ3RoKSBsZW4gPSBhcnIubGVuZ3RoO1xuICBmb3IgKHZhciBpID0gMCwgYXJyMiA9IG5ldyBBcnJheShsZW4pOyBpIDwgbGVuOyBpKyspIGFycjJbaV0gPSBhcnJbaV07XG4gIHJldHVybiBhcnIyO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/src/_array_like_to_array.mjs\n"));

/***/ }),

/***/ "./node_modules/@swc/helpers/src/_array_without_holes.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@swc/helpers/src/_array_without_holes.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _arrayWithoutHoles; }\n/* harmony export */ });\n/* harmony import */ var _array_like_to_array_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_array_like_to_array.mjs */ \"./node_modules/@swc/helpers/src/_array_like_to_array.mjs\");\n\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return (0,_array_like_to_array_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(arr);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9fYXJyYXlfd2l0aG91dF9ob2xlcy5tanMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkQ7O0FBRTVDO0FBQ2YsaUNBQWlDLG9FQUFpQjtBQUNsRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9fYXJyYXlfd2l0aG91dF9ob2xlcy5tanM/Yjk4NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2FycmF5TGlrZVRvQXJyYXkgZnJvbSAnLi9fYXJyYXlfbGlrZV90b19hcnJheS5tanMnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBfYXJyYXlXaXRob3V0SG9sZXMoYXJyKSB7XG4gIGlmIChBcnJheS5pc0FycmF5KGFycikpIHJldHVybiBfYXJyYXlMaWtlVG9BcnJheShhcnIpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/src/_array_without_holes.mjs\n"));

/***/ }),

/***/ "./node_modules/@swc/helpers/src/_iterable_to_array.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@swc/helpers/src/_iterable_to_array.mjs ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _iterableToArray; }\n/* harmony export */ });\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9faXRlcmFibGVfdG9fYXJyYXkubWpzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9zcmMvX2l0ZXJhYmxlX3RvX2FycmF5Lm1qcz8yMGZjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIF9pdGVyYWJsZVRvQXJyYXkoaXRlcikge1xuICBpZiAodHlwZW9mIFN5bWJvbCAhPT0gXCJ1bmRlZmluZWRcIiAmJiBpdGVyW1N5bWJvbC5pdGVyYXRvcl0gIT0gbnVsbCB8fCBpdGVyW1wiQEBpdGVyYXRvclwiXSAhPSBudWxsKSByZXR1cm4gQXJyYXkuZnJvbShpdGVyKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/src/_iterable_to_array.mjs\n"));

/***/ }),

/***/ "./node_modules/@swc/helpers/src/_non_iterable_spread.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@swc/helpers/src/_non_iterable_spread.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _nonIterableSpread; }\n/* harmony export */ });\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9fbm9uX2l0ZXJhYmxlX3NwcmVhZC5tanMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9fbm9uX2l0ZXJhYmxlX3NwcmVhZC5tanM/NTFkMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBfbm9uSXRlcmFibGVTcHJlYWQoKSB7XG4gIHRocm93IG5ldyBUeXBlRXJyb3IoXCJJbnZhbGlkIGF0dGVtcHQgdG8gc3ByZWFkIG5vbi1pdGVyYWJsZSBpbnN0YW5jZS5cXFxcbkluIG9yZGVyIHRvIGJlIGl0ZXJhYmxlLCBub24tYXJyYXkgb2JqZWN0cyBtdXN0IGhhdmUgYSBbU3ltYm9sLml0ZXJhdG9yXSgpIG1ldGhvZC5cIik7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/src/_non_iterable_spread.mjs\n"));

/***/ }),

/***/ "./node_modules/@swc/helpers/src/_to_consumable_array.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@swc/helpers/src/_to_consumable_array.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _toConsumableArray; }\n/* harmony export */ });\n/* harmony import */ var _array_without_holes_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_array_without_holes.mjs */ \"./node_modules/@swc/helpers/src/_array_without_holes.mjs\");\n/* harmony import */ var _iterable_to_array_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_iterable_to_array.mjs */ \"./node_modules/@swc/helpers/src/_iterable_to_array.mjs\");\n/* harmony import */ var _non_iterable_spread_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./_non_iterable_spread.mjs */ \"./node_modules/@swc/helpers/src/_non_iterable_spread.mjs\");\n/* harmony import */ var _unsupported_iterable_to_array_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./_unsupported_iterable_to_array.mjs */ \"./node_modules/@swc/helpers/src/_unsupported_iterable_to_array.mjs\");\n\n\n\n\n\nfunction _toConsumableArray(arr) {\n  return (0,_array_without_holes_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(arr) || (0,_iterable_to_array_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(arr) || (0,_unsupported_iterable_to_array_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(arr) || (0,_non_iterable_spread_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9fdG9fY29uc3VtYWJsZV9hcnJheS5tanMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMkQ7QUFDSjtBQUNJO0FBQ21COztBQUUvRDtBQUNmLFNBQVMsb0VBQWlCLFNBQVMsa0VBQWUsU0FBUyw4RUFBMEIsU0FBUyxvRUFBaUI7QUFDL0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9zcmMvX3RvX2NvbnN1bWFibGVfYXJyYXkubWpzPzkwYzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFycmF5V2l0aG91dEhvbGVzIGZyb20gJy4vX2FycmF5X3dpdGhvdXRfaG9sZXMubWpzJztcbmltcG9ydCBpdGVyYWJsZVRvQXJyYXkgZnJvbSAnLi9faXRlcmFibGVfdG9fYXJyYXkubWpzJztcbmltcG9ydCBub25JdGVyYWJsZVNwcmVhZCBmcm9tICcuL19ub25faXRlcmFibGVfc3ByZWFkLm1qcyc7XG5pbXBvcnQgdW5zdXBwb3J0ZWRJdGVyYWJsZVRvQXJyYXkgZnJvbSAnLi9fdW5zdXBwb3J0ZWRfaXRlcmFibGVfdG9fYXJyYXkubWpzJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gX3RvQ29uc3VtYWJsZUFycmF5KGFycikge1xuICByZXR1cm4gYXJyYXlXaXRob3V0SG9sZXMoYXJyKSB8fCBpdGVyYWJsZVRvQXJyYXkoYXJyKSB8fCB1bnN1cHBvcnRlZEl0ZXJhYmxlVG9BcnJheShhcnIpIHx8IG5vbkl0ZXJhYmxlU3ByZWFkKCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/src/_to_consumable_array.mjs\n"));

/***/ }),

/***/ "./node_modules/@swc/helpers/src/_unsupported_iterable_to_array.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/@swc/helpers/src/_unsupported_iterable_to_array.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _unsupportedIterableToArray; }\n/* harmony export */ });\n/* harmony import */ var _array_like_to_array_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_array_like_to_array.mjs */ \"./node_modules/@swc/helpers/src/_array_like_to_array.mjs\");\n\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return (0,_array_like_to_array_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(n);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))\n    return (0,_array_like_to_array_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(o, minLen);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9fdW5zdXBwb3J0ZWRfaXRlcmFibGVfdG9fYXJyYXkubWpzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJEOztBQUU1QztBQUNmO0FBQ0Esb0NBQW9DLG9FQUFpQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0VBQWlCO0FBQzVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvc3JjL191bnN1cHBvcnRlZF9pdGVyYWJsZV90b19hcnJheS5tanM/ZjYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2FycmF5TGlrZVRvQXJyYXkgZnJvbSAnLi9fYXJyYXlfbGlrZV90b19hcnJheS5tanMnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBfdW5zdXBwb3J0ZWRJdGVyYWJsZVRvQXJyYXkobywgbWluTGVuKSB7XG4gIGlmICghbykgcmV0dXJuO1xuICBpZiAodHlwZW9mIG8gPT09IFwic3RyaW5nXCIpIHJldHVybiBfYXJyYXlMaWtlVG9BcnJheShvLCBtaW5MZW4pO1xuICB2YXIgbiA9IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChvKS5zbGljZSg4LCAtMSk7XG4gIGlmIChuID09PSBcIk9iamVjdFwiICYmIG8uY29uc3RydWN0b3IpIG4gPSBvLmNvbnN0cnVjdG9yLm5hbWU7XG4gIGlmIChuID09PSBcIk1hcFwiIHx8IG4gPT09IFwiU2V0XCIpIHJldHVybiBBcnJheS5mcm9tKG4pO1xuICBpZiAobiA9PT0gXCJBcmd1bWVudHNcIiB8fCAvXig/OlVpfEkpbnQoPzo4fDE2fDMyKSg/OkNsYW1wZWQpP0FycmF5JC8udGVzdChuKSlcbiAgICByZXR1cm4gX2FycmF5TGlrZVRvQXJyYXkobywgbWluTGVuKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/src/_unsupported_iterable_to_array.mjs\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cindex.js&page=%2F!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);