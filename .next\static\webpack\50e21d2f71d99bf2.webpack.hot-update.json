{"c": ["pages/_app", "webpack"], "r": ["pages/index", "pages/card/[id]"], "m": ["./components/CardGrid.js", "./components/HeroCarousel.js", "./components/PriceDisplay.js", "./components/Sidebar.js", "./node_modules/@swc/helpers/src/_array_like_to_array.mjs", "./node_modules/@swc/helpers/src/_array_without_holes.mjs", "./node_modules/@swc/helpers/src/_iterable_to_array.mjs", "./node_modules/@swc/helpers/src/_non_iterable_spread.mjs", "./node_modules/@swc/helpers/src/_to_consumable_array.mjs", "./node_modules/@swc/helpers/src/_unsupported_iterable_to_array.mjs", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cindex.js&page=%2F!", "./pages/index.js", "./node_modules/@swc/helpers/src/_array_with_holes.mjs", "./node_modules/@swc/helpers/src/_non_iterable_rest.mjs", "./node_modules/@swc/helpers/src/_sliced_to_array.mjs", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Ccard%5C%5Bid%5D.js&page=%2Fcard%2F%5Bid%5D!", "./pages/card/[id].js"]}