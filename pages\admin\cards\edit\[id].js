import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import AdminLayout from '../../../../components/admin/AdminLayout';
import ImageUpload from '../../../../components/admin/ImageUpload';
import { dataOperations } from '../../../../lib/clientDataOperations';

export default function EditCard() {
  const router = useRouter();
  const { id } = router.query;
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    subtitle: '',
    year: '',
    grade: '',
    price: '',
    estimatedValue: '',
    expectedSellPrice: '',
    sellMultiplier: '1.050',
    status: '立即购买',
    endTime: '',
    condition: '',
    categoryId: '',
    visible: true,
    featured: false,
    description: '',
    images: [''],
    specifications: {},
    seller: {
      name: '',
      rating: 5.0,
      sales: 0,
      verified: true
    }
  });

  const [specificationFields, setSpecificationFields] = useState([
    { key: '', value: '' }
  ]);

  useEffect(() => {
    if (id) {
      const loadData = async () => {
        try {
          const [card, allCategories] = await Promise.all([
            dataOperations.getCard(id),
            dataOperations.getAllCategories()
          ]);

          setCategories(allCategories);

          if (card) {
            // 处理图片数据 - 确保是数组格式
            let images = [];
            if (typeof card.images === 'string') {
              try {
                images = JSON.parse(card.images);
              } catch (e) {
                console.error('Failed to parse images JSON:', e);
                images = [card.images]; // 如果解析失败，将字符串作为单个图片
              }
            } else if (Array.isArray(card.images)) {
              images = card.images;
            }

            // 确保至少有一个空的图片字段
            if (images.length === 0) {
              images = [''];
            }

            // 处理规格参数数据 - 确保是对象格式
            let specifications = {};
            if (typeof card.specifications === 'string') {
              try {
                specifications = JSON.parse(card.specifications);
              } catch (e) {
                console.error('Failed to parse specifications JSON:', e);
                specifications = {};
              }
            } else if (typeof card.specifications === 'object' && card.specifications !== null) {
              specifications = card.specifications;
            }

            setFormData({
              name: card.name || '',
              subtitle: card.subtitle || '',
              year: card.year || '',
              grade: card.grade || '',
              price: (card.price || 0).toString(),
              estimatedValue: (card.estimatedValue || '').toString(),
              expectedSellPrice: (card.expectedSellPrice || '').toString(),
              sellMultiplier: (card.sellMultiplier || 1.050).toFixed(3),
              status: card.status || '立即购买',
              endTime: card.endTime || '',
              condition: card.condition || '',
              categoryId: (card.categoryId || '').toString(),
              visible: card.visible !== false,
              featured: card.featured === true,
              description: card.description || '',
              images: images,
              specifications: specifications,
              seller: card.seller || {
                name: '',
                rating: 5.0,
                sales: 0,
                verified: true
              }
            });

            // 设置规格参数字段
            const specs = Object.entries(specifications).map(([key, value]) => ({
              key,
              value
            }));
            setSpecificationFields(specs.length > 0 ? specs : [{ key: '', value: '' }]);
          } else {
            alert('卡牌不存在');
            router.push('/admin/cards');
          }
        } catch (error) {
          console.error('Error loading card data:', error);
          alert('加载卡牌数据失败');
          router.push('/admin/cards');
        } finally {
          setLoading(false);
        }
      };

      loadData();
    }
  }, [id, router]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.startsWith('seller.')) {
      const sellerField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        seller: {
          ...(prev.seller || {}),
          [sellerField]: type === 'checkbox' ? checked :
                        type === 'number' ? parseFloat(value) : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : 
                type === 'number' ? (value === '' ? '' : parseFloat(value)) : 
                value
      }));
    }
  };

  const handleImageChange = (index, value) => {
    const newImages = [...formData.images];
    newImages[index] = value;
    setFormData(prev => ({ ...prev, images: newImages }));
  };

  const addImageField = () => {
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, '']
    }));
  };

  const removeImageField = (index) => {
    if (formData.images.length > 1) {
      const newImages = formData.images.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, images: newImages }));
    }
  };

  const handleSpecificationChange = (index, field, value) => {
    const newSpecs = [...specificationFields];
    newSpecs[index][field] = value;
    setSpecificationFields(newSpecs);
    
    // 更新formData中的specifications
    const specifications = {};
    newSpecs.forEach(spec => {
      if (spec.key && spec.value) {
        specifications[spec.key] = spec.value;
      }
    });
    setFormData(prev => ({ ...prev, specifications }));
  };

  const addSpecificationField = () => {
    setSpecificationFields(prev => [...prev, { key: '', value: '' }]);
  };

  const removeSpecificationField = (index) => {
    if (specificationFields.length > 1) {
      const newSpecs = specificationFields.filter((_, i) => i !== index);
      setSpecificationFields(newSpecs);
      
      const specifications = {};
      newSpecs.forEach(spec => {
        if (spec.key && spec.value) {
          specifications[spec.key] = spec.value;
        }
      });
      setFormData(prev => ({ ...prev, specifications }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // 验证必填字段
    if (!formData.name || !formData.price || !formData.categoryId) {
      alert('请填写所有必填字段');
      return;
    }

    // 过滤空的图片URL
    const filteredImages = formData.images.filter(img => img && img.trim() !== '');

    if (filteredImages.length === 0) {
      alert('请至少添加一张图片');
      return;
    }

    const cardData = {
      name: formData.name || null,
      subtitle: formData.subtitle || null,
      year: formData.year || null,
      grade: formData.grade || null,
      price: parseFloat(formData.price) || 0,
      original_price: parseFloat(formData.price) || 0,
      estimated_value: formData.estimatedValue ? parseFloat(formData.estimatedValue) : null,
      expected_sell_price: formData.expectedSellPrice ? parseFloat(formData.expectedSellPrice) : null,
      sell_multiplier: formData.sellMultiplier ? parseFloat(formData.sellMultiplier) : 1.050,
      status: formData.status || '立即购买',
      end_time: formData.endTime || null,
      condition_desc: formData.condition || null,
      category_id: parseInt(formData.categoryId) || null,
      visible: formData.visible !== false,
      featured: formData.featured === true,
      description: formData.description || null,
      images: filteredImages,
      specifications: formData.specifications || {},
      seller_info: formData.seller || null
    };

    try {
      const updatedCard = await dataOperations.updateCard(id, cardData);
      if (updatedCard) {
        alert('卡牌更新成功！');
        router.push('/admin/cards');
      } else {
        alert('更新失败，请重试');
      }
    } catch (error) {
      alert('更新失败，请重试');
      console.error(error);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <>
      <Head>
        <title>编辑卡牌 - 管理后台</title>
      </Head>

      <AdminLayout>
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">编辑卡牌</h1>
            <p className="text-gray-600 mt-1">修改卡牌的详细信息</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* 基本信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">基本信息</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    卡牌名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如：Michael Jordan 新秀卡"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">副标题</label>
                  <input
                    type="text"
                    name="subtitle"
                    value={formData.subtitle}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如：1986 Fleer Basketball #57 PSA 10"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    分类 <span className="text-red-500">*</span>
                  </label>
                  <select
                    name="categoryId"
                    value={formData.categoryId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">选择分类</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">发行年份</label>
                  <input
                    type="text"
                    name="year"
                    value={formData.year}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如：1986"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">评级</label>
                  <input
                    type="text"
                    name="grade"
                    value={formData.grade}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如：PSA 10"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">品相</label>
                  <input
                    type="text"
                    name="condition"
                    value={formData.condition}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如：完美品相"
                  />
                </div>
              </div>
            </div>

            {/* 价格和状态 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">价格和状态</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    当前价格 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    name="price"
                    value={formData.price}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>



                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">固定价格</label>
                  <input
                    type="number"
                    name="estimatedValue"
                    value={formData.estimatedValue}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                    min="0"
                    step="0.01"
                  />
                  {formData.estimatedValue && formData.sellMultiplier && parseFloat(formData.estimatedValue) > 0 && parseFloat(formData.sellMultiplier) > 0 && (
                    <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="text-xs font-medium text-blue-800 mb-2">前端估值页面预览：</div>
                      <div className="grid grid-cols-1 gap-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">固定价格:</span>
                          <span className="font-medium text-gray-900">
                            RM{parseFloat(formData.estimatedValue).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">预估抛售价格:</span>
                          <span className="font-medium text-green-700">
                            RM{(parseFloat(formData.estimatedValue) * parseFloat(formData.sellMultiplier)).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">抛售系数:</span>
                          <span className="font-medium text-gray-600">
                            {parseFloat(formData.sellMultiplier).toFixed(3)}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expected Sell Price (手动设置)
                  </label>
                  <input
                    type="number"
                    name="expectedSellPrice"
                    value={formData.expectedSellPrice}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0"
                    min="0"
                    step="0.01"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    如果设置此字段，将覆盖自动计算的Expected Sell Price (估值价格 × 1.05)
                  </p>
                  {formData.expectedSellPrice && parseFloat(formData.expectedSellPrice) > 0 && (
                    <div className="mt-2 p-2 bg-green-50 rounded border border-green-200">
                      <div className="text-xs font-medium text-green-800">
                        手动设置的Expected Sell Price: RM{parseFloat(formData.expectedSellPrice).toFixed(2)}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    抛售系数
                  </label>
                  <input
                    type="number"
                    name="sellMultiplier"
                    value={formData.sellMultiplier}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="1.050"
                    min="1.000"
                    max="5.000"
                    step="0.001"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    用于计算预估抛售价格的系数（固定价格 × 抛售系数）
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">销售状态</label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="立即购买">立即购买</option>
                    <option value="竞拍中">竞拍中</option>
                    <option value="估值中">估值中</option>
                  </select>
                </div>

                {formData.status === '竞拍中' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">结束时间</label>
                    <input
                      type="text"
                      name="endTime"
                      value={formData.endTime}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="例如：2小时"
                    />
                  </div>
                )}
              </div>

              <div className="mt-6 flex items-center space-x-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="visible"
                    checked={formData.visible}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">在前台显示</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="featured"
                    checked={formData.featured}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">设为精选</span>
                </label>
              </div>
            </div>

            {/* 图片 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">卡牌图片</h2>
              <div className="space-y-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="flex-1">
                      <ImageUpload
                        value={image}
                        onChange={(value) => handleImageChange(index, value)}
                        placeholder={`图片 ${index + 1} - 输入URL或上传本地图片`}
                      />
                    </div>
                    {formData.images.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeImageField(index)}
                        className="p-2 text-red-600 hover:text-red-800 mt-2"
                      >
                        <i className="ri-delete-bin-line"></i>
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addImageField}
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
                >
                  <i className="ri-add-line"></i>
                  <span>添加图片</span>
                </button>
              </div>
            </div>

            {/* 描述 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">卡牌描述</h2>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="详细描述这张卡牌的特点、历史背景、收藏价值等..."
              />
            </div>

            {/* 规格参数 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">规格参数</h2>
              <div className="space-y-4">
                {specificationFields.map((spec, index) => (
                  <div key={index} className="grid grid-cols-2 gap-4">
                    <input
                      type="text"
                      value={spec.key}
                      onChange={(e) => handleSpecificationChange(index, 'key', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="参数名称"
                    />
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={spec.value}
                        onChange={(e) => handleSpecificationChange(index, 'value', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="参数值"
                      />
                      {specificationFields.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeSpecificationField(index)}
                          className="p-2 text-red-600 hover:text-red-800"
                        >
                          <i className="ri-delete-bin-line"></i>
                        </button>
                      )}
                    </div>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addSpecificationField}
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
                >
                  <i className="ri-add-line"></i>
                  <span>添加参数</span>
                </button>
              </div>
            </div>

            {/* 卖家信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">卖家信息</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">卖家名称</label>
                  <input
                    type="text"
                    name="seller.name"
                    value={formData.seller?.name || ''}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="卖家名称"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">评分</label>
                  <input
                    type="number"
                    name="seller.rating"
                    value={formData.seller?.rating || 5.0}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                    max="5"
                    step="0.1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">交易次数</label>
                  <input
                    type="number"
                    name="seller.sales"
                    value={formData.seller?.sales || 0}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                  />
                </div>
              </div>

              <div className="mt-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="seller.verified"
                    checked={formData.seller?.verified || false}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">认证卖家</span>
                </label>
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="flex items-center justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                保存修改
              </button>
            </div>
          </form>
        </div>
      </AdminLayout>
    </>
  );
}
