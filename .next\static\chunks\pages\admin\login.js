/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/admin/login"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cadmin%5Clogin.js&page=%2Fadmin%2Flogin!":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cadmin%5Clogin.js&page=%2Fadmin%2Flogin! ***!
  \***********************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/admin/login\",\n      function () {\n        return __webpack_require__(/*! ./pages/admin/login.js */ \"./pages/admin/login.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/admin/login\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDcHktaWRlJTVDd3VkYWxhbmctc2hvcCU1Q3BhZ2VzJTVDYWRtaW4lNUNsb2dpbi5qcyZwYWdlPSUyRmFkbWluJTJGbG9naW4hLmpzIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsc0RBQXdCO0FBQy9DO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz8zOTQ4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvYWRtaW4vbG9naW5cIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3BhZ2VzL2FkbWluL2xvZ2luLmpzXCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9hZG1pbi9sb2dpblwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cadmin%5Clogin.js&page=%2Fadmin%2Flogin!\n"));

/***/ }),

/***/ "./pages/admin/login.js":
/*!******************************!*\
  !*** ./pages/admin/login.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminLogin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_define_property_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_define_property.mjs */ \"./node_modules/@swc/helpers/src/_define_property.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\nvar _s = $RefreshSig$();\n\n\n\nfunction AdminLogin() {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"\",\n        password: \"\",\n        accessCode: \"\"\n    }), formData = ref[0], setFormData = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), loading = ref1[0], setLoading = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), error = ref2[0], setError = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), success = ref3[0], setSuccess = ref3[1];\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 强制清除认证状态的函数\n    var forceLogout = function() {\n        console.log(\"Force logout - clearing all auth data...\");\n        // 清除所有可能的cookie变体\n        var cookiesToClear = [\n            \"adminToken\"\n        ];\n        cookiesToClear.forEach(function(cookieName) {\n            document.cookie = \"\".concat(cookieName, \"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT\");\n            document.cookie = \"\".concat(cookieName, \"=; Path=/admin; Expires=Thu, 01 Jan 1970 00:00:00 GMT\");\n            document.cookie = \"\".concat(cookieName, \"=; Domain=\").concat(window.location.hostname, \"; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT\");\n        });\n        // 清除localStorage和sessionStorage中可能的认证数据\n        localStorage.removeItem(\"adminToken\");\n        localStorage.removeItem(\"user\");\n        sessionStorage.removeItem(\"adminToken\");\n        sessionStorage.removeItem(\"user\");\n        console.log(\"Force logout completed\");\n    };\n    // 检查是否已经登录\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var checkAuth = function() {\n            var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function() {\n                var urlParams, response, data, error;\n                return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            _state.trys.push([\n                                0,\n                                5,\n                                ,\n                                6\n                            ]);\n                            console.log(\"Checking authentication status...\");\n                            urlParams = new URLSearchParams(window.location.search);\n                            if (urlParams.get(\"logout\") === \"true\") {\n                                console.log(\"Logout parameter detected, skipping auth check\");\n                                forceLogout();\n                                // 清除URL参数\n                                window.history.replaceState({}, document.title, window.location.pathname);\n                                return [\n                                    2\n                                ];\n                            }\n                            return [\n                                4,\n                                fetch(\"/api/admin/auth/check\", {\n                                    credentials: \"include\",\n                                    cache: \"no-cache\",\n                                    headers: {\n                                        \"Cache-Control\": \"no-cache\",\n                                        \"Pragma\": \"no-cache\"\n                                    }\n                                })\n                            ];\n                        case 1:\n                            response = _state.sent();\n                            console.log(\"Auth check response status:\", response.status);\n                            if (!response.ok) return [\n                                3,\n                                3\n                            ];\n                            return [\n                                4,\n                                response.json()\n                            ];\n                        case 2:\n                            data = _state.sent();\n                            console.log(\"Auth check response data:\", data);\n                            if (data.authenticated) {\n                                // 已经登录，重定向到管理后台\n                                console.log(\"Already authenticated, redirecting to admin\");\n                                window.location.href = \"/admin\";\n                            } else {\n                                console.log(\"Not authenticated\");\n                                // 强制清除认证状态\n                                forceLogout();\n                            }\n                            return [\n                                3,\n                                4\n                            ];\n                        case 3:\n                            console.log(\"Auth check failed, response status:\", response.status);\n                            // 强制清除认证状态\n                            forceLogout();\n                            _state.label = 4;\n                        case 4:\n                            return [\n                                3,\n                                6\n                            ];\n                        case 5:\n                            error = _state.sent();\n                            // 未登录，继续显示登录页面\n                            console.log(\"Auth check error:\", error);\n                            // 强制清除认证状态\n                            forceLogout();\n                            return [\n                                3,\n                                6\n                            ];\n                        case 6:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function checkAuth() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        // 延迟检查，避免立即重定向\n        var timer = setTimeout(checkAuth, 1000); // 增加延迟时间\n        return function() {\n            return clearTimeout(timer);\n        };\n    }, []);\n    var handleChange = function(e) {\n        var _target = e.target, name = _target.name, value = _target.value;\n        setFormData(function(prev) {\n            return (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({}, prev), (0,_swc_helpers_src_define_property_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({}, name, value));\n        });\n        // 清除错误信息\n        if (error) setError(\"\");\n    };\n    var handleSubmit = function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function(e) {\n            var response, data, error;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        e.preventDefault();\n                        setLoading(true);\n                        setError(\"\");\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            4,\n                            5,\n                            6\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/admin/auth/login\", {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify(formData)\n                            })\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 3:\n                        data = _state.sent();\n                        if (response.ok) {\n                            // 登录成功，重定向到管理后台\n                            console.log(\"Login successful, redirecting to admin\");\n                            setSuccess(\"登录成功！正在跳转...\");\n                            // 延迟跳转，让用户看到成功消息\n                            setTimeout(function() {\n                                window.location.href = \"/admin\";\n                            }, 1000);\n                        } else {\n                            setError(data.message || \"登录失败\");\n                        }\n                        return [\n                            3,\n                            6\n                        ];\n                    case 4:\n                        error = _state.sent();\n                        console.error(\"Login error:\", error);\n                        setError(\"网络错误，请重试\");\n                        return [\n                            3,\n                            6\n                        ];\n                    case 5:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 6:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function handleSubmit(e) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"管理员登录 - CardMarket\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"CardMarket管理后台登录\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-shield-user-line text-white text-2xl\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                    children: \"管理员登录\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-600\",\n                                    children: \"请输入您的管理员账号信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                className: \"space-y-6\",\n                                onSubmit: handleSubmit,\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-error-warning-line text-red-500 text-lg mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-700 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this),\n                                    success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-check-line text-green-500 text-lg mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-700 text-sm\",\n                                                    children: success\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"username\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"用户名\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-user-line text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"username\",\n                                                        name: \"username\",\n                                                        type: \"text\",\n                                                        required: true,\n                                                        value: formData.username,\n                                                        onChange: handleChange,\n                                                        className: \"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/50\",\n                                                        placeholder: \"请输入用户名\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"password\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"密码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-lock-line text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"password\",\n                                                        name: \"password\",\n                                                        type: \"password\",\n                                                        required: true,\n                                                        value: formData.password,\n                                                        onChange: handleChange,\n                                                        className: \"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/50\",\n                                                        placeholder: \"请输入密码\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"accessCode\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"访问码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-key-line text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"accessCode\",\n                                                        name: \"accessCode\",\n                                                        type: \"text\",\n                                                        required: true,\n                                                        value: formData.accessCode,\n                                                        onChange: handleChange,\n                                                        className: \"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/50\",\n                                                        placeholder: \"请输入访问码\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"登录中...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-login-circle-line mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"登录\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\admin\\\\login.js\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AdminLogin, \"f00NiLdBeEZFndrXMyatjVN4dRI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminLogin;\nvar _c;\n$RefreshReg$(_c, \"AdminLogin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/admin/login.js\n"));

/***/ }),

/***/ "./node_modules/@swc/helpers/src/_object_spread_props.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@swc/helpers/src/_object_spread_props.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ _objectSpreadProps; }\n/* harmony export */ });\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\n\nfunction _objectSpreadProps(target, source) {\n  source = source != null ? source : {}\n  if (Object.getOwnPropertyDescriptors) {\n    Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n  } else {\n    ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(\n        target,\n        key,\n        Object.getOwnPropertyDescriptor(source, key)\n      );\n    });\n  }\n\n  return target;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL3NyYy9fb2JqZWN0X3NwcmVhZF9wcm9wcy5tanMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9zcmMvX29iamVjdF9zcHJlYWRfcHJvcHMubWpzPzJlODMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gb3duS2V5cyhvYmplY3QsIGVudW1lcmFibGVPbmx5KSB7XG4gIHZhciBrZXlzID0gT2JqZWN0LmtleXMob2JqZWN0KTtcbiAgaWYgKE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMpIHtcbiAgICB2YXIgc3ltYm9scyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMob2JqZWN0KTtcbiAgICBpZiAoZW51bWVyYWJsZU9ubHkpIHtcbiAgICAgIHN5bWJvbHMgPSBzeW1ib2xzLmZpbHRlcihmdW5jdGlvbiAoc3ltKSB7XG4gICAgICAgIHJldHVybiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG9iamVjdCwgc3ltKS5lbnVtZXJhYmxlO1xuICAgICAgfSk7XG4gICAgfVxuICAgIGtleXMucHVzaC5hcHBseShrZXlzLCBzeW1ib2xzKTtcbiAgfVxuICByZXR1cm4ga2V5cztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gX29iamVjdFNwcmVhZFByb3BzKHRhcmdldCwgc291cmNlKSB7XG4gIHNvdXJjZSA9IHNvdXJjZSAhPSBudWxsID8gc291cmNlIDoge31cbiAgaWYgKE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzKSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXModGFyZ2V0LCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhzb3VyY2UpKTtcbiAgfSBlbHNlIHtcbiAgICBvd25LZXlzKE9iamVjdChzb3VyY2UpKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShcbiAgICAgICAgdGFyZ2V0LFxuICAgICAgICBrZXksXG4gICAgICAgIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Ioc291cmNlLCBrZXkpXG4gICAgICApO1xuICAgIH0pO1xuICB9XG5cbiAgcmV0dXJuIHRhcmdldDtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/src/_object_spread_props.mjs\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cadmin%5Clogin.js&page=%2Fadmin%2Flogin!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);