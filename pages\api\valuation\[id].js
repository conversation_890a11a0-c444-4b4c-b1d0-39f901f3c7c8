const { cardsDAO } = require('../../../lib/mysqlDatabase.js');
const { t } = require('../../../lib/i18n.js');

export default async function handler(req, res) {
  const { id } = req.query;

  if (req.method === 'GET') {
    // 获取卡牌的估值信息
    try {
      const card = await cardsDAO.getById(id);

      if (!card) {
        return res.status(404).json({ error: t('valuation.cardNotFound') });
      }

      // 使用固定价格（estimatedValue）和抛售系数计算
      const fixedPrice = card.estimatedValue || card.price;
      const sellMultiplier = card.sellMultiplier || 1.050;

      const valuationData = {
        id: card.id,
        name: card.name,
        currentPrice: card.price,
        estimatedValue: fixedPrice,
        sellMultiplier: sellMultiplier,
        buyPrice: fixedPrice.toFixed(2),
        sellPrice: (fixedPrice * sellMultiplier).toFixed(2),
        priceChange: card.estimatedValue ? ((card.estimatedValue - card.price) / card.price * 100).toFixed(2) : '0.00',
        lastUpdated: card.updatedAt
      };

      res.status(200).json(valuationData);
    } catch (error) {
      console.error('Error getting valuation:', error);
      res.status(500).json({ error: t('valuation.getValuationFailed') });
    }
  } else if (req.method === 'POST') {
    // 提交估值信息
    try {
      const { buyPrice, sellPrice, duration } = req.body;

      if (!buyPrice || !sellPrice || !duration) {
        return res.status(400).json({ error: t('valuation.missingParameters') });
      }

      const card = await cardsDAO.getById(id);

      if (!card) {
        return res.status(404).json({ error: t('valuation.cardNotFound') });
      }

      // 这里可以保存用户的估值提交到数据库
      // 暂时只返回成功响应
      const valuationSubmission = {
        cardId: parseInt(id),
        buyPrice: parseFloat(buyPrice),
        sellPrice: parseFloat(sellPrice),
        duration: duration,
        submittedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (duration === '1day' ? 24 : 48) * 60 * 60 * 1000).toISOString()
      };

      // TODO: 保存到估值提交表
      console.log('估值提交:', valuationSubmission);

      res.status(200).json({
        success: true,
        message: t('valuation.submitValuationSuccess'),
        data: valuationSubmission
      });
    } catch (error) {
      console.error('Error submitting valuation:', error);
      res.status(500).json({ error: t('valuation.submitValuationFailed') });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
