"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/valuation/[id]",{

/***/ "./pages/valuation/[id].js":
/*!*********************************!*\
  !*** ./pages/valuation/[id].js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ValuationPage; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/Header */ \"./components/Header.js\");\n/* harmony import */ var _components_ImageGallery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ImageGallery */ \"./components/ImageGallery.js\");\n/* harmony import */ var _components_CountdownTimer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/CountdownTimer */ \"./components/CountdownTimer.js\");\n/* harmony import */ var _hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../hooks/useSiteConfig */ \"./hooks/useSiteConfig.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../lib/i18n */ \"./lib/i18n.js\");\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ValuationPage() {\n    _s();\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    var id = router.query.id;\n    var ref = (0,_hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__.useSiteConfig)(), siteConfig = ref.siteConfig, configLoading = ref.loading;\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), card = ref1[0], setCard = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), loading = ref2[0], setLoading = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), error = ref3[0], setError = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), buyPrice = ref4[0], setBuyPrice = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), sellPrice = ref5[0], setSellPrice = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1day\"), selectedDuration = ref6[0], setSelectedDuration = ref6[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (id) {\n            loadCard();\n        }\n    }, [\n        id\n    ]);\n    function loadCard() {\n        return _loadCard.apply(this, arguments);\n    }\n    function _loadCard() {\n        _loadCard = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            var response, cardData, fixedPrice, sellMultiplier, error;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            4,\n                            5\n                        ]);\n                        setLoading(true);\n                        return [\n                            4,\n                            fetch(\"/api/cards/\".concat(id))\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            if (response.status === 404) {\n                                setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                            } else {\n                                setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                            }\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        cardData = _state.sent();\n                        setCard(cardData);\n                        fixedPrice = cardData.estimatedValue || cardData.price;\n                        sellMultiplier = cardData.sellMultiplier || 1.050;\n                        setBuyPrice(fixedPrice.toFixed(2));\n                        setSellPrice((fixedPrice * sellMultiplier).toFixed(2));\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error loading card:\", error);\n                        setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return _loadCard.apply(this, arguments);\n    }\n    var handleSubmitValuation = function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            var response, _$error, error;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            5,\n                            ,\n                            6\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/valuation/\".concat(id), {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    buyPrice: buyPrice,\n                                    sellPrice: sellPrice,\n                                    duration: selectedDuration\n                                })\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            2\n                        ];\n                        alert(\"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.success\"), \"!\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPrice\"), \": RM\").concat(buyPrice, \"\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPrice\"), \": RM\").concat(sellPrice, \"\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.duration\"), \": \").concat(selectedDuration === \"1day\" ? (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.oneDayOption\") : (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.twoDayOption\")));\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 3:\n                        _$error = _state.sent();\n                        alert(\"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"), \": \").concat(_$error.error || (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\")));\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            3,\n                            6\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        console.error(\"Error submitting valuation:\", error);\n                        alert((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                        return [\n                            3,\n                            6\n                        ];\n                    case 6:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function handleSubmitValuation() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    if (loading || configLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    siteConfig: siteConfig\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !card) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    siteConfig: siteConfig\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl text-gray-400 mb-4\",\n                                    children: \"\\uD83D\\uDE1E\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                    children: error || (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-blue-600 hover:text-blue-800 transition-colors duration-200\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.back\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            card.name,\n                            \" - \",\n                            (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\"),\n                            \" - \",\n                            siteConfig.title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.title\"), \" - \").concat(card.name)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        siteConfig: siteConfig\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-sm mx-auto px-4 py-4 sm:max-w-2xl sm:px-6 sm:py-6 lg:max-w-7xl lg:px-8 lg:py-8 xl:max-w-[1600px] xl:px-12 2xl:max-w-[1800px] 2xl:px-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center space-x-2 text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6 lg:mb-8 overflow-x-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            className: \"hover:text-purple-600 transition-colors duration-200 whitespace-nowrap\",\n                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.home\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-arrow-right-s-line\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/card/\".concat(card.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            className: \"hover:text-purple-600 transition-colors duration-200 whitespace-nowrap\",\n                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.details\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-arrow-right-s-line\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-purple-600 font-medium whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 sticky top-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-square bg-gray-100 p-4 sm:p-6 lg:p-8\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full rounded-lg overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageGallery__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    images: card.images\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium\",\n                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 sm:p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 173,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.status\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 174,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 172,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-green-600 bg-green-100 px-2 py-1 rounded-md\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.available\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 176,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.remainingTime\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-gray-900 bg-white px-2 py-1 rounded-md border\",\n                                                                            children: [\n                                                                                \"1 \",\n                                                                                (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"time.day\")\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg p-4 text-center border border-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.year\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: card.year\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg p-4 text-center border border-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.grade\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: card.grade\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 196,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-1\",\n                                                            children: card.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: card.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-900 px-6 py-4 text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-lg font-semibold text-gray-200\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.fixedPriceSection\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold mt-1\",\n                                                                        children: [\n                                                                            \"RM\",\n                                                                            parseFloat(buyPrice).toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.includesFees\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-300\",\n                                                                        children: \"(BP)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-6 py-4 bg-gray-50 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-red-600 mb-2 font-medium\",\n                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.remainingTime\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CountdownTimer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            duration: selectedDuration === \"1day\" ? 24 : 48,\n                                                            onComplete: function() {\n                                                                return alert((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.valuationExpired\"));\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-medium text-gray-700 mb-3\",\n                                                                    children: \"固定价格设置\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                            children: \"固定价格\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm\",\n                                                                                    children: \"RM\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 247,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    value: buyPrice,\n                                                                                    readOnly: true,\n                                                                                    className: \"w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-900 bg-gray-50 cursor-not-allowed transition-all duration-200\",\n                                                                                    placeholder: \"1,590.00\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs\",\n                                                                                            children: \"?\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 257,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 256,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 255,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t border-gray-200 my-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: \"预估抛售价格\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600 text-xs\",\n                                                                            children: \"基于固定价格和抛售系数计算的预估抛售价格\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.duration\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 277,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    value: selectedDuration,\n                                                                                    onChange: function(e) {\n                                                                                        return setSelectedDuration(e.target.value);\n                                                                                    },\n                                                                                    className: \"w-full p-2.5 border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-900 focus:border-gray-900 text-sm bg-white transition-all duration-200\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"1day\",\n                                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.oneDayOption\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 285,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"2day\",\n                                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.twoDayOption\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 286,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-green-50 rounded-lg p-4 mb-4 border border-green-200\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-green-700 font-medium mb-1\",\n                                                                                        children: \"预估抛售价格\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 293,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-2xl font-bold text-green-800 mb-1\",\n                                                                                        children: [\n                                                                                            \"RM\",\n                                                                                            parseFloat(sellPrice).toLocaleString()\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 294,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-green-600\",\n                                                                                        children: [\n                                                                                            \"预估利润: +RM\",\n                                                                                            parseFloat(sellPrice - buyPrice).toLocaleString()\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 297,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleSubmitValuation,\n                                                                                className: \"w-full bg-gray-900 hover:bg-gray-800 text-white py-3 px-4 rounded-md font-medium text-sm transition-all duration-200\",\n                                                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.startValuation\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-4xl mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4 text-center\",\n                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.usageGuide\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"\\uD83D\\uDCB0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPriceGuide\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPriceDesc\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"\\uD83D\\uDCC8\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPriceGuide\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPriceDesc\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ValuationPage, \"iaMa5+/wjPnPymBxeZku1OdikGM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__.useSiteConfig\n    ];\n});\n_c = ValuationPage;\nvar _c;\n$RefreshReg$(_c, \"ValuationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/valuation/[id].js\n"));

/***/ })

});