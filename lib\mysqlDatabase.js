require('dotenv').config();
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'cardmarket',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
let pool;

function getPool() {
  if (!pool) {
    pool = mysql.createPool({
      ...dbConfig,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
  }
  return pool;
}

// 执行查询
async function query(sql, params = []) {
  try {
    const pool = getPool();
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

// 生成ID (MySQL会自动生成，这里保留兼容性)
function generateId(items) {
  if (!items || items.length === 0) return 1;
  return Math.max(...items.map(item => item.id || 0)) + 1;
}

// 管理员用户操作
const adminUsersDAO = {
  async create(userData) {
    try {
      const { username, password, email, role = 'admin' } = userData;
      const result = await query(
        'INSERT INTO admin_users (username, password, email, role) VALUES (?, ?, ?, ?)',
        [username, password, email, role]
      );
      return result.insertId;
    } catch (error) {
      console.error('Error creating admin user:', error);
      throw error;
    }
  },

  async getByUsername(username) {
    try {
      const rows = await query('SELECT * FROM admin_users WHERE username = ? AND status = "active"', [username]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Error getting admin user by username:', error);
      throw error;
    }
  },

  async getById(id) {
    try {
      const rows = await query('SELECT * FROM admin_users WHERE id = ? AND status = "active"', [id]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Error getting admin user by id:', error);
      throw error;
    }
  },

  async updateLastLogin(id) {
    try {
      await query('UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', [id]);
      return true;
    } catch (error) {
      console.error('Error updating last login:', error);
      throw error;
    }
  },

  async getAll() {
    try {
      const rows = await query('SELECT id, username, email, role, status, last_login, created_at FROM admin_users ORDER BY created_at DESC');
      return rows;
    } catch (error) {
      console.error('Error getting all admin users:', error);
      return [];
    }
  },

  async updatePassword(id, newPassword) {
    try {
      await query('UPDATE admin_users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [newPassword, id]);
      return true;
    } catch (error) {
      console.error('Error updating password:', error);
      throw error;
    }
  },

  async updateProfile(id, updates) {
    try {
      const { password, ...profileUpdates } = updates;
      const fields = [];
      const values = [];

      Object.keys(profileUpdates).forEach(key => {
        if (profileUpdates[key] !== undefined) {
          fields.push(`${key} = ?`);
          values.push(profileUpdates[key]);
        }
      });

      if (fields.length > 0) {
        fields.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);

        const sql = `UPDATE admin_users SET ${fields.join(', ')} WHERE id = ?`;
        await query(sql, values);
      }

      return true;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  }
};

// 网站配置操作
const siteConfigDAO = {
  async getAll() {
    try {
      const rows = await query('SELECT config_key, config_value FROM site_config');
      const config = {};
      rows.forEach(row => {
        config[row.config_key] = row.config_value;
      });
      
      // 如果没有配置，返回空值
      if (Object.keys(config).length === 0) {
        return {
          title: '',
          description: '',
          logo: ''
        };
      }
      
      return config;
    } catch (error) {
      console.error('Error getting site config:', error);
      return {
        title: '',
        description: '',
        logo: ''
      };
    }
  },

  async get(key) {
    try {
      const rows = await query('SELECT config_value FROM site_config WHERE config_key = ?', [key]);
      return rows.length > 0 ? rows[0].config_value : null;
    } catch (error) {
      console.error('Error getting site config key:', error);
      return null;
    }
  },

  async set(key, value) {
    try {
      // 将boolean值转换为字符串存储
      const stringValue = typeof value === 'boolean' ? value.toString() : value;
      await query(
        'INSERT INTO site_config (config_key, config_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE config_value = VALUES(config_value)',
        [key, stringValue]
      );
    } catch (error) {
      console.error('Error setting site config:', error);
      throw error;
    }
  },

  async setMultiple(configs) {
    try {
      for (const [key, value] of Object.entries(configs)) {
        // 将boolean值转换为字符串存储
        const stringValue = typeof value === 'boolean' ? value.toString() : value;
        await this.set(key, stringValue);
      }
    } catch (error) {
      console.error('Error setting multiple site configs:', error);
      throw error;
    }
  }
};

// 导航菜单操作
const navigationDAO = {
  async getAll() {
    try {
      const rows = await query('SELECT * FROM navigation ORDER BY sort_order ASC, id ASC');
      return rows;
    } catch (error) {
      console.error('Error getting navigation:', error);
      return [];
    }
  },

  async getVisible() {
    try {
      const rows = await query('SELECT * FROM navigation WHERE visible = TRUE ORDER BY sort_order ASC, id ASC');
      return rows;
    } catch (error) {
      console.error('Error getting visible navigation:', error);
      return [];
    }
  },

  async create(data) {
    try {
      const result = await query(
        'INSERT INTO navigation (name, href, visible, sort_order) VALUES (?, ?, ?, ?)',
        [data.name, data.href, data.visible !== false, data.sort_order || 0]
      );
      return result.insertId;
    } catch (error) {
      console.error('Error creating navigation:', error);
      throw error;
    }
  },

  async update(id, data) {
    try {
      const updateFields = [];
      const updateValues = [];
      
      if (data.name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(data.name);
      }
      if (data.href !== undefined) {
        updateFields.push('href = ?');
        updateValues.push(data.href);
      }
      if (data.visible !== undefined) {
        updateFields.push('visible = ?');
        updateValues.push(data.visible);
      }
      if (data.sort_order !== undefined) {
        updateFields.push('sort_order = ?');
        updateValues.push(data.sort_order);
      }
      
      if (updateFields.length > 0) {
        updateValues.push(parseInt(id));
        await query(
          `UPDATE navigation SET ${updateFields.join(', ')} WHERE id = ?`,
          updateValues
        );
      }
    } catch (error) {
      console.error('Error updating navigation:', error);
      throw error;
    }
  },

  async delete(id) {
    try {
      await query('DELETE FROM navigation WHERE id = ?', [parseInt(id)]);
    } catch (error) {
      console.error('Error deleting navigation:', error);
      throw error;
    }
  }
};

// 分类操作
const categoriesDAO = {
  async getAll() {
    try {
      const rows = await query('SELECT * FROM categories ORDER BY sort_order ASC, id ASC');
      return rows;
    } catch (error) {
      console.error('Error getting categories:', error);
      return [];
    }
  },

  async getVisible() {
    try {
      const rows = await query('SELECT * FROM categories WHERE visible = TRUE ORDER BY sort_order ASC, id ASC');
      return rows;
    } catch (error) {
      console.error('Error getting visible categories:', error);
      return [];
    }
  },

  async getById(id) {
    try {
      const rows = await query('SELECT * FROM categories WHERE id = ?', [parseInt(id)]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Error getting category by id:', error);
      return null;
    }
  },

  async create(data) {
    try {
      const result = await query(
        'INSERT INTO categories (name, icon, count, visible, sort_order) VALUES (?, ?, ?, ?, ?)',
        [data.name, data.icon, 0, data.visible !== false, data.sort_order || 0]
      );
      return result.insertId;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  },

  async update(id, data) {
    try {
      const updateFields = [];
      const updateValues = [];

      if (data.name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(data.name);
      }
      if (data.icon !== undefined) {
        updateFields.push('icon = ?');
        updateValues.push(data.icon);
      }
      // 移除count字段的手动更新，count只能通过updateCount方法自动计算
      if (data.visible !== undefined) {
        updateFields.push('visible = ?');
        updateValues.push(data.visible);
      }
      if (data.sort_order !== undefined) {
        updateFields.push('sort_order = ?');
        updateValues.push(data.sort_order);
      }

      if (updateFields.length > 0) {
        updateValues.push(parseInt(id));
        await query(
          `UPDATE categories SET ${updateFields.join(', ')} WHERE id = ?`,
          updateValues
        );
      }
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  },

  async delete(id) {
    try {
      await query('DELETE FROM categories WHERE id = ?', [parseInt(id)]);
    } catch (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
  },

  async updateCount(id) {
    try {
      const countResult = await query(
        'SELECT COUNT(*) as count FROM cards WHERE category_id = ? AND visible = TRUE',
        [parseInt(id)]
      );
      const count = countResult[0].count;
      // 直接更新count字段，绕过update方法的限制
      await query('UPDATE categories SET count = ? WHERE id = ?', [count, parseInt(id)]);
    } catch (error) {
      console.error('Error updating category count:', error);
      throw error;
    }
  },

  // 修复所有分类的计数
  async fixAllCounts() {
    try {
      const categories = await this.getAll();
      for (const category of categories) {
        await this.updateCount(category.id);
      }
      console.log('All category counts have been fixed');
    } catch (error) {
      console.error('Error fixing category counts:', error);
      throw error;
    }
  }
};

// 卡牌操作
const cardsDAO = {
  async getAll() {
    try {
      const rows = await query('SELECT * FROM cards ORDER BY created_at DESC');

      // 安全解析JSON字段的函数
      const safeJSONParse = (jsonString, defaultValue = {}) => {
        if (!jsonString) return defaultValue;
        try {
          const parsed = JSON.parse(jsonString);
          // 如果解析结果是字符串，可能是双重编码，再次解析
          if (typeof parsed === 'string') {
            return JSON.parse(parsed);
          }
          return parsed;
        } catch (error) {
          console.error('JSON parse error:', error, 'for string:', jsonString);
          return defaultValue;
        }
      };

      // 解析JSON字段
      return rows.map(card => ({
        ...card,
        images: safeJSONParse(card.images, []),
        specifications: safeJSONParse(card.specifications, {}),
        seller: safeJSONParse(card.seller_info, null),
        // 兼容性字段映射，确保数值类型
        price: card.price ? parseFloat(card.price) : 0,
        originalPrice: card.original_price ? parseFloat(card.original_price) : null,
        estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,
        expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,
        endTime: card.end_time,
        condition: card.condition_desc,
        categoryId: card.category_id,
        createdAt: card.created_at,
        updatedAt: card.updated_at,
        // 状态值转换：将中文状态转换为英文键值
        status: card.status === '立即购买' ? 'buyNow' :
                card.status === '竞拍中' ? 'auctioning' :
                card.status,
        // 转换布尔值
        visible: Boolean(card.visible),
        featured: Boolean(card.featured)
      }));
    } catch (error) {
      console.error('Error getting cards:', error);
      return [];
    }
  },

  async getVisible() {
    try {
      const rows = await query('SELECT * FROM cards WHERE visible = 1 ORDER BY created_at DESC');
      return rows.map(card => ({
        ...card,
        images: card.images ? JSON.parse(card.images) : [],
        specifications: card.specifications ? JSON.parse(card.specifications) : {},
        seller: card.seller_info ? JSON.parse(card.seller_info) : null,
        // 兼容性字段映射，确保数值类型
        price: card.price ? parseFloat(card.price) : 0,
        originalPrice: card.original_price ? parseFloat(card.original_price) : null,
        estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,
        expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,
        fixedPrice: card.fixed_price ? parseFloat(card.fixed_price) : null,
        sellMultiplier: card.sell_multiplier ? parseFloat(Number(card.sell_multiplier).toFixed(3)) : 1.050,
        endTime: card.end_time,
        condition: card.condition_desc,
        categoryId: card.category_id,
        createdAt: card.created_at,
        updatedAt: card.updated_at,
        // 状态值转换：将中文状态转换为英文键值
        status: card.status === '立即购买' ? 'buyNow' :
                card.status === '竞拍中' ? 'auctioning' :
                card.status,
        // 转换布尔值
        visible: Boolean(card.visible),
        featured: Boolean(card.featured)
      }));
    } catch (error) {
      console.error('Error getting visible cards:', error);
      return [];
    }
  },

  async getFeatured() {
    try {
      const rows = await query('SELECT * FROM cards WHERE visible = 1 AND featured = 1 ORDER BY created_at DESC');
      return rows.map(card => ({
        ...card,
        images: card.images ? JSON.parse(card.images) : [],
        specifications: card.specifications ? JSON.parse(card.specifications) : {},
        seller: card.seller_info ? JSON.parse(card.seller_info) : null,
        // 兼容性字段映射，确保数值类型
        price: card.price ? parseFloat(card.price) : 0,
        originalPrice: card.original_price ? parseFloat(card.original_price) : null,
        estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,
        expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,
        endTime: card.end_time,
        condition: card.condition_desc,
        categoryId: card.category_id,
        createdAt: card.created_at,
        updatedAt: card.updated_at,
        // 状态值转换：将中文状态转换为英文键值
        status: card.status === '立即购买' ? 'buyNow' :
                card.status === '竞拍中' ? 'auctioning' :
                card.status,
        // 转换布尔值
        visible: Boolean(card.visible),
        featured: Boolean(card.featured)
      }));
    } catch (error) {
      console.error('Error getting featured cards:', error);
      return [];
    }
  },

  async getById(id) {
    try {
      const parsedId = parseInt(id);

      if (isNaN(parsedId)) {
        return null;
      }

      const rows = await query('SELECT * FROM cards WHERE id = ?', [parsedId]);

      if (rows.length === 0) {
        return null;
      }

      const card = rows[0];

      // 安全解析JSON字段的函数
      const safeJSONParse = (jsonString, defaultValue = {}) => {
        if (!jsonString) return defaultValue;
        try {
          const parsed = JSON.parse(jsonString);
          // 如果解析结果是字符串，可能是双重编码，再次解析
          if (typeof parsed === 'string') {
            return JSON.parse(parsed);
          }
          return parsed;
        } catch (error) {
          console.error('JSON parse error:', error, 'for string:', jsonString);
          return defaultValue;
        }
      };

      return {
        ...card,
        images: safeJSONParse(card.images, []),
        specifications: safeJSONParse(card.specifications, {}),
        seller: safeJSONParse(card.seller_info, null),
        // 兼容性字段映射，确保数值类型
        price: card.price ? parseFloat(card.price) : 0,
        originalPrice: card.original_price ? parseFloat(card.original_price) : null,
        estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,
        expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,
        fixedPrice: card.fixed_price ? parseFloat(card.fixed_price) : null,
        sellMultiplier: card.sell_multiplier ? parseFloat(Number(card.sell_multiplier).toFixed(3)) : 1.050,
        endTime: card.end_time,
        condition: card.condition_desc,
        categoryId: card.category_id,
        createdAt: card.created_at,
        updatedAt: card.updated_at,
        // 状态值转换：将中文状态转换为英文键值
        status: card.status === '立即购买' ? 'buyNow' :
                card.status === '竞拍中' ? 'auctioning' :
                card.status,
        // 转换布尔值
        visible: Boolean(card.visible),
        featured: Boolean(card.featured)
      };
    } catch (error) {
      console.error('Error getting card by id:', error);
      return null;
    }
  },

  async getByCategory(categoryId) {
    try {
      const rows = await query('SELECT * FROM cards WHERE category_id = ? AND visible = 1 ORDER BY created_at DESC', [parseInt(categoryId)]);
      return rows.map(card => ({
        ...card,
        images: card.images ? JSON.parse(card.images) : [],
        specifications: card.specifications ? JSON.parse(card.specifications) : {},
        seller: card.seller_info ? JSON.parse(card.seller_info) : null,
        // 兼容性字段映射，确保数值类型
        price: card.price ? parseFloat(card.price) : 0,
        originalPrice: card.original_price ? parseFloat(card.original_price) : null,
        estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,
        expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,
        fixedPrice: card.fixed_price ? parseFloat(card.fixed_price) : null,
        sellMultiplier: card.sell_multiplier ? parseFloat(Number(card.sell_multiplier).toFixed(3)) : 1.050,
        endTime: card.end_time,
        condition: card.condition_desc,
        categoryId: card.category_id,
        createdAt: card.created_at,
        updatedAt: card.updated_at,
        // 状态值转换：将中文状态转换为英文键值
        status: card.status === '立即购买' ? 'buyNow' :
                card.status === '竞拍中' ? 'auctioning' :
                card.status,
        // 转换布尔值
        visible: Boolean(card.visible),
        featured: Boolean(card.featured)
      }));
    } catch (error) {
      console.error('Error getting cards by category:', error);
      return [];
    }
  },

  async create(data) {
    try {
      // 处理字段映射和JSON序列化，确保undefined转换为null
      const cardData = {
        name: data.name || null,
        subtitle: data.subtitle || null,
        year: data.year || null,
        grade: data.grade || null,
        price: data.price || 0,
        original_price: data.originalPrice || data.original_price || null,
        estimated_value: data.estimatedValue || data.estimated_value || null,
        expected_sell_price: data.expectedSellPrice || data.expected_sell_price || null,
        fixed_price: data.fixedPrice || data.fixed_price || null,
        sell_multiplier: data.sellMultiplier || data.sell_multiplier || 1.050,
        status: data.status || '立即购买',
        end_time: data.endTime || data.end_time || null,
        condition_desc: data.condition || data.condition_desc || null,
        category_id: data.categoryId || data.category_id || null,
        visible: data.visible !== false,
        featured: data.featured === true,
        description: data.description || null,
        images: JSON.stringify(data.images || []),
        specifications: JSON.stringify(data.specifications || {}),
        seller_info: JSON.stringify(data.seller_info || {})
      };

      const result = await query(
        `INSERT INTO cards (
          name, subtitle, year, grade, price, original_price, estimated_value, expected_sell_price,
          fixed_price, sell_multiplier, status, end_time,
          condition_desc, category_id, visible, featured, description, images,
          specifications, seller_info
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          cardData.name, cardData.subtitle, cardData.year, cardData.grade,
          cardData.price, cardData.original_price, cardData.estimated_value, cardData.expected_sell_price,
          cardData.fixed_price, cardData.sell_multiplier, cardData.status, cardData.end_time,
          cardData.condition_desc, cardData.category_id, cardData.visible, cardData.featured,
          cardData.description, cardData.images, cardData.specifications, cardData.seller_info
        ]
      );

      // 更新分类计数
      if (cardData.category_id) {
        await categoriesDAO.updateCount(cardData.category_id);
      }

      return result.insertId;
    } catch (error) {
      console.error('Error creating card:', error);
      throw error;
    }
  },

  async update(id, data) {
    try {
      // 获取更新前的卡牌信息，用于分类计数更新
      const oldCard = await this.getById(id);

      const updateFields = [];
      const updateValues = [];

      if (data.name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(data.name);
      }
      if (data.subtitle !== undefined) {
        updateFields.push('subtitle = ?');
        updateValues.push(data.subtitle);
      }
      if (data.year !== undefined) {
        updateFields.push('year = ?');
        updateValues.push(data.year);
      }
      if (data.grade !== undefined) {
        updateFields.push('grade = ?');
        updateValues.push(data.grade);
      }
      if (data.price !== undefined) {
        updateFields.push('price = ?');
        updateValues.push(data.price);
      }
      if (data.originalPrice !== undefined || data.original_price !== undefined) {
        updateFields.push('original_price = ?');
        updateValues.push(data.originalPrice || data.original_price);
      }
      if (data.estimatedValue !== undefined || data.estimated_value !== undefined) {
        updateFields.push('estimated_value = ?');
        updateValues.push(data.estimatedValue || data.estimated_value);
      }
      if (data.expectedSellPrice !== undefined || data.expected_sell_price !== undefined) {
        updateFields.push('expected_sell_price = ?');
        updateValues.push(data.expectedSellPrice || data.expected_sell_price);
      }
      if (data.fixedPrice !== undefined || data.fixed_price !== undefined) {
        updateFields.push('fixed_price = ?');
        updateValues.push(data.fixedPrice || data.fixed_price);
      }
      if (data.sellMultiplier !== undefined || data.sell_multiplier !== undefined) {
        updateFields.push('sell_multiplier = ?');
        updateValues.push(data.sellMultiplier || data.sell_multiplier);
      }
      if (data.status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(data.status);
      }
      if (data.endTime !== undefined || data.end_time !== undefined) {
        updateFields.push('end_time = ?');
        updateValues.push(data.endTime || data.end_time);
      }
      if (data.condition !== undefined || data.condition_desc !== undefined) {
        updateFields.push('condition_desc = ?');
        updateValues.push(data.condition || data.condition_desc);
      }
      if (data.categoryId !== undefined || data.category_id !== undefined) {
        updateFields.push('category_id = ?');
        updateValues.push(data.categoryId || data.category_id);
      }
      if (data.visible !== undefined) {
        updateFields.push('visible = ?');
        updateValues.push(data.visible);
      }
      if (data.featured !== undefined) {
        updateFields.push('featured = ?');
        updateValues.push(data.featured);
      }
      if (data.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(data.description);
      }
      if (data.images !== undefined) {
        updateFields.push('images = ?');
        updateValues.push(JSON.stringify(data.images));
      }
      if (data.specifications !== undefined) {
        updateFields.push('specifications = ?');
        updateValues.push(JSON.stringify(data.specifications));
      }

      if (updateFields.length > 0) {
        updateValues.push(parseInt(id));
        await query(
          `UPDATE cards SET ${updateFields.join(', ')} WHERE id = ?`,
          updateValues
        );

        // 更新分类计数 - 修复逻辑
        const needsCountUpdate = (
          (data.categoryId !== undefined || data.category_id !== undefined) ||
          (data.visible !== undefined)
        );

        if (needsCountUpdate && oldCard) {
          const newCategoryId = data.categoryId || data.category_id;
          const oldCategoryId = oldCard.categoryId;

          // 如果分类发生变化，需要更新新旧两个分类的计数
          if (newCategoryId !== undefined && newCategoryId !== oldCategoryId) {
            // 更新新分类计数
            await categoriesDAO.updateCount(newCategoryId);
            // 更新旧分类计数
            if (oldCategoryId) {
              await categoriesDAO.updateCount(oldCategoryId);
            }
          } else if (data.visible !== undefined && oldCard.categoryId) {
            // 如果只是可见性发生变化，更新当前分类计数
            await categoriesDAO.updateCount(oldCard.categoryId);
          }
        }
      }
    } catch (error) {
      console.error('Error updating card:', error);
      throw error;
    }
  },

  async delete(id) {
    try {
      // 获取卡牌信息以便更新分类计数
      const card = await this.getById(id);

      await query('DELETE FROM cards WHERE id = ?', [parseInt(id)]);

      // 更新分类计数
      if (card && card.categoryId) {
        await categoriesDAO.updateCount(card.categoryId);
      }
    } catch (error) {
      console.error('Error deleting card:', error);
      throw error;
    }
  },

  async search(keyword) {
    try {
      const rows = await query(
        `SELECT * FROM cards
         WHERE visible = TRUE
         AND (name LIKE ? OR subtitle LIKE ? OR description LIKE ?)
         ORDER BY created_at DESC`,
        [`%${keyword}%`, `%${keyword}%`, `%${keyword}%`]
      );

      return rows.map(card => ({
        ...card,
        images: card.images ? JSON.parse(card.images) : [],
        specifications: card.specifications ? JSON.parse(card.specifications) : {},
        seller_info: card.seller_info ? JSON.parse(card.seller_info) : {},
        // 兼容性字段映射，确保数值类型
        price: card.price ? parseFloat(card.price) : 0,
        originalPrice: card.original_price ? parseFloat(card.original_price) : null,
        estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,
        expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,
        endTime: card.end_time,
        condition: card.condition_desc,
        categoryId: card.category_id,
        seller: card.seller_info ? JSON.parse(card.seller_info) : {},
        createdAt: card.created_at,
        updatedAt: card.updated_at
      }));
    } catch (error) {
      console.error('Error searching cards:', error);
      return [];
    }
  }
};

// 初始化数据库表
async function initDatabase() {
  try {
    const pool = getPool();

    console.log('Initializing MySQL database...');

    // 创建数据库（如果不存在）
    await pool.query(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);

    // 使用数据库
    await pool.query(`USE ${dbConfig.database}`);

    // 创建管理员用户表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS admin_users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
        password VARCHAR(255) NOT NULL COMMENT '密码哈希',
        email VARCHAR(100) COMMENT '邮箱',
        role VARCHAR(20) DEFAULT 'admin' COMMENT '角色',
        status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
        last_login TIMESTAMP NULL COMMENT '最后登录时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_username (username),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表'
    `);

    // 创建网站配置表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS site_config (
        id INT PRIMARY KEY AUTO_INCREMENT,
        config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键名',
        config_value TEXT COMMENT '配置值',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_config_key (config_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站配置表'
    `);

    // 创建导航菜单表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS navigation (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL COMMENT '菜单名称',
        href VARCHAR(255) NOT NULL COMMENT '链接地址',
        visible BOOLEAN DEFAULT TRUE COMMENT '是否可见',
        sort_order INT DEFAULT 0 COMMENT '排序顺序',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_visible_sort (visible, sort_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导航菜单表'
    `);

    // 创建分类表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS categories (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL COMMENT '分类名称',
        icon VARCHAR(100) COMMENT '图标类名',
        count INT DEFAULT 0 COMMENT '卡牌数量',
        visible BOOLEAN DEFAULT TRUE COMMENT '是否可见',
        sort_order INT DEFAULT 0 COMMENT '排序顺序',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_visible_sort (visible, sort_order),
        INDEX idx_name (name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡牌分类表'
    `);

    // 创建卡牌表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS cards (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL COMMENT '钱币名称',
        subtitle VARCHAR(255) COMMENT '副标题',
        year VARCHAR(10) COMMENT '发行年份',
        grade VARCHAR(50) COMMENT '评级',
        price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
        original_price DECIMAL(10,2) COMMENT '原价',
        estimated_value DECIMAL(10,2) COMMENT '估值价格',
        expected_sell_price DECIMAL(10,2) COMMENT '预期售价',
        fixed_price DECIMAL(10,2) COMMENT '固定价格',
        sell_multiplier DECIMAL(5,3) DEFAULT 1.050 COMMENT '抛售系数',
        status VARCHAR(50) DEFAULT '立即购买' COMMENT '状态',
        end_time VARCHAR(100) COMMENT '结束时间',
        condition_desc VARCHAR(255) COMMENT '品相描述',
        category_id INT COMMENT '分类ID',
        visible BOOLEAN DEFAULT TRUE COMMENT '是否可见',
        featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
        description TEXT COMMENT '详细描述',
        images TEXT COMMENT '图片数组(JSON字符串)',
        specifications TEXT COMMENT '规格参数(JSON字符串)',
        seller_info TEXT COMMENT '卖家信息(JSON字符串)',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
        INDEX idx_category_visible (category_id, visible),
        INDEX idx_featured_visible (featured, visible),
        INDEX idx_status (status),
        INDEX idx_price (price),
        INDEX idx_estimated_value (estimated_value),
        INDEX idx_expected_sell_price (expected_sell_price),
        INDEX idx_fixed_price (fixed_price),
        INDEX idx_sell_multiplier (sell_multiplier),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡牌信息表'
    `);

    // 添加估值字段（如果表已存在）
    try {
      await pool.query(`
        ALTER TABLE cards ADD COLUMN estimated_value DECIMAL(10,2) COMMENT '估值价格'
      `);
      console.log('Added estimated_value column to cards table');
    } catch (error) {
      // 字段可能已存在，忽略错误
      if (!error.message.includes('Duplicate column name')) {
        console.log('estimated_value column may already exist');
      }
    }

    // 添加预期售价字段（如果表已存在）
    try {
      await pool.query(`
        ALTER TABLE cards ADD COLUMN expected_sell_price DECIMAL(10,2) COMMENT '预期售价'
      `);
      console.log('Added expected_sell_price column to cards table');
    } catch (error) {
      // 字段可能已存在，忽略错误
      if (!error.message.includes('Duplicate column name')) {
        console.log('expected_sell_price column may already exist');
      }
    }

    // 添加固定价格字段（如果表已存在）
    try {
      await pool.query(`
        ALTER TABLE cards ADD COLUMN fixed_price DECIMAL(10,2) COMMENT '固定价格'
      `);
      console.log('Added fixed_price column to cards table');
    } catch (error) {
      // 字段可能已存在，忽略错误
      if (!error.message.includes('Duplicate column name')) {
        console.log('fixed_price column may already exist');
      }
    }

    // 添加抛售系数字段（如果表已存在）
    try {
      await pool.query(`
        ALTER TABLE cards ADD COLUMN sell_multiplier DECIMAL(5,3) DEFAULT 1.050 COMMENT '抛售系数'
      `);
      console.log('Added sell_multiplier column to cards table');
    } catch (error) {
      // 字段可能已存在，忽略错误
      if (!error.message.includes('Duplicate column name')) {
        console.log('sell_multiplier column may already exist');
      }
    }

    console.log('MySQL database tables created successfully');
    return true;
  } catch (error) {
    console.error('MySQL database initialization error:', error);
    throw error;
  }
}

// 填充初始数据（仅创建必要的基础结构）
async function seedDatabase() {
  try {
    console.log('Setting up basic database structure...');

    // 仅初始化空的网站配置，让用户自己设置
    const existingConfig = await siteConfigDAO.get('title');
    if (!existingConfig) {
      await siteConfigDAO.setMultiple({
        title: '',
        description: '',
        logo: '',
        whatsapp_enabled: 'false',
        whatsapp_phone: '',
        whatsapp_message: '您好！我想咨询一下您的古币纸币。',
        whatsapp_tooltip: '需要帮助？在WhatsApp上与我们聊天！'
      });
      console.log('Basic site configuration structure created');
    }

    console.log('Basic database structure setup completed');
    return true;
  } catch (error) {
    console.error('MySQL database seed error:', error);
    throw error;
  }
}

module.exports = {
  adminUsersDAO,
  siteConfigDAO,
  navigationDAO,
  categoriesDAO,
  cardsDAO,
  initDatabase,
  seedDatabase,
  query,
  getPool
};
