// 测试精度问题
console.log('=== 测试抛售系数精度问题 ===\n');

// 模拟数据库存储的值（DECIMAL(5,3)）
const dbValues = ['1.300', '1.297', '1.3'];

console.log('1. 测试数据库读取时的parseFloat转换:');
dbValues.forEach(val => {
  const parsed = parseFloat(val);
  console.log(`数据库值: "${val}" -> parseFloat: ${parsed} -> toFixed(3): ${parsed.toFixed(3)}`);
});

console.log('\n2. 测试前端输入处理:');
const userInputs = ['1.3', '1.30', '1.300'];
userInputs.forEach(input => {
  const parsed = parseFloat(input);
  console.log(`用户输入: "${input}" -> parseFloat: ${parsed} -> 存储值: ${parsed}`);
});

console.log('\n3. 测试可能的精度丢失场景:');
// 模拟一些可能导致精度丢失的操作
const testValue = 1.3;
console.log(`原始值: ${testValue}`);
console.log(`JSON.stringify后: ${JSON.stringify(testValue)}`);
console.log(`JSON.parse后: ${JSON.parse(JSON.stringify(testValue))}`);
console.log(`toString后parseFloat: ${parseFloat(testValue.toString())}`);

console.log('\n4. 测试数据库DECIMAL精度:');
// 模拟DECIMAL(5,3)的行为
function simulateDecimal53(value) {
  // DECIMAL(5,3)应该精确存储3位小数
  const num = parseFloat(value);
  return Math.round(num * 1000) / 1000; // 保留3位小数的精确计算
}

const testCases = [1.3, 1.297, 1.2999, 1.3001];
testCases.forEach(val => {
  console.log(`输入: ${val} -> DECIMAL(5,3): ${simulateDecimal53(val)}`);
});

console.log('\n5. 检查可能的数据源问题:');
// 检查1.297是否来自某种计算
console.log('1.3 * 0.997:', 1.3 * 0.997);
console.log('1.3 - 0.003:', 1.3 - 0.003);
console.log('1.3 * (1 - 0.003/1.3):', 1.3 * (1 - 0.003/1.3));
