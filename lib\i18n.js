// 国际化配置文件
export const translations = {
  en: {
    // 通用
    common: {
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save',
      delete: 'Delete',
      edit: 'Edit',
      view: 'View',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      search: 'Search',
      filter: 'Filter',
      sort: 'Sort',
      more: 'More'
    },

    // 导航
    nav: {
      home: 'Home',
      allCoin: 'All Coin',
      auction: 'Auction',
      premium: 'Premium',
      buyNow: 'Buy Now',
      sportsCards: 'Commemorative Coins',
      tradingCards: 'Vintage Banknotes',
      valuation: 'Valuation',
      admin: 'Admin',
      about: 'About Us'
    },

    // Header
    header: {
      searchPlaceholder: 'Search coins, banknotes, etc...',
      menu: 'Menu'
    },

    // 公司简介页面
    about: {
      title: 'About Us',
      subtitle: 'myduitlama is a platform focused on collecting, trading and auctioning ancient coins and old banknotes from Malaysia and countries around the world. We are committed to providing authentic, professional and high-quality collectibles and services to every collector and investor.',
      aboutUsTitle: 'About Us',
      aboutUsContent1: 'We specialize in the acquisition, sale and auction of old banknotes and coins, while providing consulting services for international authoritative grading (such as PMG, PCGS), and can commission the auction of high-value collections on behalf of clients.',
      aboutUsContent2: 'We have trading and auction channels covering China, the United States, the United Kingdom, Japan and other major countries, and are committed to sharing collecting knowledge and market conditions to help clients make more informed decisions in collecting and investing.',
      professionalPlatform: 'Professional Collection Platform',
      trustedExpert: 'Trusted Ancient Coin and Banknote Trading Expert',
      contactUs: 'Contact Us',
      contactInfo: 'Contact Information',
      email: 'Email',
      phone: 'Phone'
    },

    // 侧边栏
    sidebar: {
      categories: 'Coin Categories',
      allCoin: 'All Coin'
    },

    // 分类翻译
    category: {
      '入门': 'Entry Level',
      '高端': 'High End',
      '收藏级': 'Collector Grade',
      '纪念币': 'Commemorative Coins',
      '古典纸币': 'Vintage Banknotes',
      '体育卡牌': 'Sports Cards',
      '游戏卡牌': 'Trading Cards'
    },

    // 首页
    home: {
      title: 'Featured Coins & Banknotes',
      subtitle: 'Curated high-value collectible coins and banknotes with real-time trading status',
      buyNowTab: 'Buy Now',
      valuationTab: 'Valuation',
      buyNowMode: 'Buy Now Prices',
      valuationMode: 'Valuation Prices',
      buyNowDesc: 'Current available purchase prices',
      valuationDesc: 'Professional valuations based on market data',
      viewDetails: 'View Details',
      startValuation: 'Start Valuation',
      buyNow: 'Buy Now',
      makeOffer: 'Make Offer',
      heroTitle1: 'Rare Coin Collection',
      heroSubtitle1: 'Historical Currency Series',
      heroTitle2: 'Vintage Banknotes',
      heroSubtitle2: 'Limited Edition Collection',
      heroTitle3: 'Commemorative Coins',
      heroSubtitle3: 'Special Issue Series',
      hotRecommendation: 'Hot Recommendation',
      weeklyPopular: 'Most popular coin categories this week'
    },

    // 钱币相关
    coin: {
      name: 'Coin Name',
      year: 'Year',
      grade: 'Grade',
      price: 'Price',
      originalPrice: 'Original Price',
      estimatedValue: 'Estimated Value',
      purchasePrice: 'Purchase Price',
      status: 'Status',
      endTime: 'End Time',
      seller: 'Seller',
      condition: 'Condition',
      rarity: 'Rarity',
      category: 'Category',
      description: 'Description',
      images: 'Images',
      auctioning: 'Auctioning',
      buyNowStatus: 'Buy Now',
      available: 'Available',
      noReserve: 'No Reserve',
      details: 'Details',
      loading: 'Loading...',
      invalidId: 'Invalid item ID',
      notFound: 'Item not found',
      loadError: 'Failed to load item information',
      sortBy: 'Sort by',
      sortFeatured: 'Featured',
      sortPriceHigh: 'Price: High to Low',
      sortPriceLow: 'Price: Low to High',
      sortNewest: 'Newest',
      priceType: 'Price Type',
      estimatedPrice: 'Estimated Price',
      loadingMore: 'Loading more...',
      noMoreCoins: 'No more coins',
      valuation: 'Valuation',
      buyNow: 'Buy Now',
      higherThan: 'Higher than',
      lowerThan: 'Lower than',
      purchasePrice: 'Purchase Price',
      priceBasedOnMarket: 'Prices based on current market conditions, updated daily',
      clickToView: 'Click to view larger image',
      loading: 'Loading...',
      loadingInfo: 'Loading collection information...',
      qualityAssurance: 'Quality Assurance',
      qualityDescription: 'All banknotes are professionally graded and certified to ensure quality and authenticity. We promise that every coin or banknote undergoes strict quality inspection to provide you with the most reliable collecting experience.',
      qualityGuarantee: 'Quality Guarantee',
      strictQualityStandards: 'Strict quality inspection standards',
      professionalService: 'Professional Service',
      itemDescription: 'Item Description',
      noDescription: 'No description available',
      collectionAdvice: 'Collection Advice',
      collectionTip: 'This coin or banknote is a rare collectible. It is recommended to store it properly, avoiding direct sunlight and humid environments.',
      detailedSpecs: 'Detailed Specifications',
      noSpecsInfo: 'No detailed specification information available',
      issueYear: 'Issue Year',
      unknown: 'Unknown',
      gradeLevel: 'Grade Level',
      grading: 'Grading',
      status: 'Status',
      conditionDesc: 'Condition Description',
      excellent: 'Excellent',
      afterSalesService: 'After-sales Service',
      professionalSupport: 'Professional customer service team support',
      professionalCertification: 'Professional Certification',
      authorityCertification: 'Authoritative institution grading certification',
      backToHome: 'Back to Home',
      itemDetails: 'Item Details',
      leftImageArea: 'Image Area',
      rightInfoArea: 'Product Information Area',
      topTitleFavorite: 'Title and Favorite Button',
      statusLabel: 'Status Label',
      middlePriceInfo: 'Price Information',
      save: 'Save',
      additionalProductInfo: 'Additional Product Information',
      bottomActionButtons: 'Action Buttons',
      valuationPageLink: 'Valuation Page Link',
      qualityAssuranceBlock: 'Quality Assurance Block',
      guaranteeFeatures: 'Guarantee Features',
      detailedInfo: 'Detailed Information'
    },

    // 时间相关
    time: {
      days: 'Days',
      day: 'day',
      hours: 'Hours',
      minutes: 'Min',
      seconds: 'Sec',
      expired: 'Expired',
      expiringSoon: 'Valuation expiring soon, please process in time',
      endsIn: 'Ends in',
      hoursLeft: 'hours left',
      dayLeft: 'day left',
      daysLeft: 'days left',
      ended: 'Ended'
    },

    // 估值页面
    valuation: {
      title: 'Valuation',
      buyPrice: 'Buy Price',
      sellPrice: 'Sell Price',
      remainingTime: 'Time Remaining',
      duration: 'Sell Duration',
      expectedPrice: 'Expected Sell Price',
      expectedProfit: 'Expected Profit',
      customPrice: 'Custom Buy Price',
      fixedPrice: 'Fixed Price',
      fixedPriceSection: 'Fixed Price',
      fixedPriceSetting: 'Fixed Price Setting',
      estimatedSellPrice: 'Estimated Sell Price',
      estimatedSellPriceDesc: 'Estimated sell price based on fixed price and sell multiplier',
      estimatedProfit: 'Estimated Profit',
      expectedReturn: 'Expected Return',
      higherReturn: 'Higher Returns',
      priceOptions: 'Price Options',
      oneDayOption: '1 Day Sale (Stable Returns)',
      twoDayOption: '2 Day Sale (Higher Potential)',
      startValuation: 'Start Valuation',
      valuationExpired: 'Valuation Expired',
      valuationExpiring: 'Valuation expiring soon, please process in time',
      includesFees: 'Includes Fees',
      usageGuide: 'Usage Guide',
      buyPriceGuide: 'Buy Price Setting',
      buyPriceDesc: 'Set the maximum price you are willing to pay for this coin. The system will match you with the best available price based on market conditions.',
      sellPriceGuide: 'Sell Price Estimation',
      sellPriceDesc: 'Estimated selling price based on market trends for the selected time period to help you make the best investment decisions.',
      // API错误信息
      cardNotFound: 'Card not found',
      getValuationFailed: 'Failed to get valuation information',
      missingParameters: 'Missing required parameters',
      submitValuationFailed: 'Failed to submit valuation',
      submitValuationSuccess: 'Valuation submitted successfully'
    },

    // WhatsApp客服
    whatsapp: {
      contactUs: 'Contact Us via WhatsApp',
      tooltip: 'Need help? Chat with us on WhatsApp!',
      defaultMessage: 'Hello! I would like to inquire about your coins and banknotes.'
    }
  },

  zh: {
    // 通用
    common: {
      loading: '加载中...',
      error: '错误',
      success: '成功',
      cancel: '取消',
      confirm: '确认',
      save: '保存',
      delete: '删除',
      edit: '编辑',
      view: '查看',
      back: '返回',
      next: '下一步',
      previous: '上一步',
      search: '搜索',
      filter: '筛选',
      sort: '排序',
      more: '更多'
    },

    // 导航
    nav: {
      home: '首页',
      allCoin: '所有古币',
      auction: '拍卖',
      premium: '精品',
      buyNow: '立即购买',
      sportsCards: '纪念币',
      tradingCards: '古典纸币',
      valuation: '估值功能',
      admin: '管理后台',
      about: '公司简介'
    },

    // Header
    header: {
      searchPlaceholder: '搜索古币、纸币等...',
      menu: '菜单'
    },

    // 公司简介页面
    about: {
      title: '公司简介',
      subtitle: 'myduitlama 是一家专注于马来西亚及世界各国古钱币、旧纸币收藏、交易与拍卖的平台。我们致力于为每一位收藏爱好者和投资者，提供真实、专业、高品质的收藏品和服务。',
      aboutUsTitle: '关于我们',
      aboutUsContent1: '我们专注于旧纸币与硬币的收购、销售与拍卖，同时提供国际权威评级（如 PMG、PCGS）的咨询服务，并可代客户委托拍卖高价值藏品。',
      aboutUsContent2: '我们拥有覆盖中国、美国、英国、日本及其他主要国家的买卖与拍卖交易渠道，致力于分享收藏知识与市场行情，帮助客户在收藏与投资中做出更明智的决策。',
      professionalPlatform: '专业收藏平台',
      trustedExpert: '值得信赖的古币旧钞交易专家',
      contactUs: '联系我们',
      contactInfo: '联系方式',
      email: '邮箱',
      phone: '电话'
    },

    // 侧边栏
    sidebar: {
      categories: '古币分类',
      allCoin: '所有古币'
    },

    // 分类翻译
    category: {
      '入门': '入门',
      '高端': '高端',
      '收藏级': '收藏级',
      '纪念币': '纪念币',
      '古典纸币': '古典纸币',
      '体育卡牌': '体育卡牌',
      '游戏卡牌': '游戏卡牌'
    },

    // 首页
    home: {
      title: '精选古币纸币',
      subtitle: '精选高价值收藏古币纸币，实时交易状态',
      buyNowTab: '立即购买',
      valuationTab: '估值功能',
      buyNowMode: '购买价格',
      valuationMode: '估值价格',
      buyNowDesc: '当前可购买价格',
      valuationDesc: '基于市场数据的专业估值',
      viewDetails: '查看详情',
      startValuation: '开始估值',
      buyNow: '立即购买',
      makeOffer: '出价',
      heroTitle1: '稀有古币收藏',
      heroSubtitle1: '历史货币系列',
      heroTitle2: '古典纸币',
      heroSubtitle2: '限量版收藏',
      heroTitle3: '纪念币系列',
      heroSubtitle3: '特别发行版',
      hotRecommendation: '热门推荐',
      weeklyPopular: '本周最受欢迎的钱币类别'
    },

    // 钱币相关
    coin: {
      name: '钱币名称',
      year: '年份',
      grade: '评级',
      price: '价格',
      originalPrice: '原价',
      estimatedValue: '市场估值',
      purchasePrice: '购买价格',
      status: '状态',
      endTime: '结束时间',
      seller: '卖家',
      condition: '品相',
      rarity: '稀有度',
      category: '分类',
      description: '描述',
      images: '图片',
      auctioning: '竞拍中',
      buyNowStatus: '立即购买',
      available: '可购买',
      noReserve: '无底价',
      details: '商品详情',
      loading: '加载中...',
      invalidId: '无效的物品ID',
      notFound: '未找到物品',
      loadError: '加载物品信息失败',
      sortBy: '排序方式',
      sortFeatured: '推荐',
      sortPriceHigh: '价格：从高到低',
      sortPriceLow: '价格：从低到高',
      sortNewest: '最新',
      priceType: '价格类型',
      estimatedPrice: '估值价格',
      loadingMore: '加载更多...',
      noMoreCoins: '没有更多钱币了',
      valuation: '估值',
      buyNow: '立即购买',
      higherThan: '高于',
      lowerThan: '低于',
      purchasePrice: '购买价',
      priceBasedOnMarket: '价格基于当前市场行情，每日更新',
      clickToView: '点击查看大图',
      loading: '加载中...',
      loadingInfo: '加载藏品信息中...',
      qualityAssurance: '品质保证',
      qualityDescription: '所有旧钞均经过专业评级认证，确保品质和真实性。我们承诺每一枚古币或旧钞都经过严格的质量检验，为您提供最可靠的收藏体验。',
      qualityGuarantee: '品质保障',
      strictQualityStandards: '严格质量检验标准',
      professionalService: '专业服务',
      itemDescription: '藏品描述',
      noDescription: '暂无描述信息',
      collectionAdvice: '收藏建议',
      collectionTip: '此古币或旧钞属于稀有收藏品，建议妥善保存，避免阳光直射和潮湿环境。',
      detailedSpecs: '详细规格',
      noSpecsInfo: '暂无详细规格信息',
      issueYear: '发行年份',
      unknown: '未知',
      gradeLevel: '品相等级',
      grading: '评级中',
      status: '状态',
      conditionDesc: '品相描述',
      excellent: '优秀',
      afterSalesService: '售后服务',
      professionalSupport: '专业客服团队支持',
      professionalCertification: '专业认证',
      authorityCertification: '权威机构评级认证',
      backToHome: '返回首页',
      itemDetails: '藏品详情',
      leftImageArea: '左侧：图片区域',
      rightInfoArea: '右侧：商品信息区域',
      topTitleFavorite: '顶部：标题和收藏按钮',
      statusLabel: '状态标签',
      middlePriceInfo: '中间：价格信息 - 使用flex-1让这部分占据剩余空间',
      save: '节省',
      additionalProductInfo: '添加一些额外的商品信息来填充空间',
      bottomActionButtons: '底部：操作按钮',
      valuationPageLink: '估值页面链接',
      qualityAssuranceBlock: '品质保证区块 - 单独一行',
      guaranteeFeatures: '添加一些保证特性',
      detailedInfo: '详细信息'
    },

    // 时间相关
    time: {
      days: '天',
      day: '天',
      hours: '小时',
      minutes: '分',
      seconds: '秒',
      expired: '已结束',
      expiringSoon: '估值即将到期，请及时处理',
      endsIn: '',
      hoursLeft: '小时后结束',
      dayLeft: '天后结束',
      daysLeft: '天后结束',
      ended: '已结束'
    },

    // 估值页面
    valuation: {
      title: '估值页面',
      buyPrice: '买入价格',
      sellPrice: '抛售价格',
      remainingTime: '剩余时间',
      duration: '抛售期限',
      expectedPrice: '预期抛售价格',
      expectedProfit: '预期收益',
      customPrice: '自定义买入价格',
      fixedPrice: '固定价格',
      fixedPriceSection: '固定价格',
      fixedPriceSetting: '固定价格设置',
      estimatedSellPrice: '预估抛售价格',
      estimatedSellPriceDesc: '基于固定价格和抛售系数计算的预估抛售价格',
      estimatedProfit: '预估利润',
      expectedReturn: '预期收益',
      higherReturn: '收益更高',
      priceOptions: '价格选项',
      oneDayOption: '1天后抛售 (稳健收益)',
      twoDayOption: '2天后抛售 (高收益潜力)',
      startValuation: '开始估值',
      valuationExpired: '估值已过期',
      valuationExpiring: '估值即将过期，请及时处理',
      includesFees: '含手续费',
      usageGuide: '使用指南',
      buyPriceGuide: '买入价格设置',
      buyPriceDesc: '设置您愿意购买此钱币的最高价格，系统会根据市场情况为您匹配最优价格。',
      sellPriceGuide: '抛售价格预估',
      sellPriceDesc: '根据市场趋势预估在选定时间后出售可获得的价格，帮助您做出最佳投资决策。',
      // API错误信息
      cardNotFound: '卡牌不存在',
      getValuationFailed: '获取估值信息失败',
      missingParameters: '缺少必要参数',
      submitValuationFailed: '提交估值失败',
      submitValuationSuccess: '估值提交成功'
    },

    // WhatsApp客服
    whatsapp: {
      contactUs: '通过WhatsApp联系我们',
      tooltip: '需要帮助？在WhatsApp上与我们聊天！',
      defaultMessage: '您好！我想咨询一下您的古币纸币。'
    }
  },

  ms: {
    // 通用
    common: {
      loading: 'Memuatkan...',
      error: 'Ralat',
      success: 'Berjaya',
      cancel: 'Batal',
      confirm: 'Sahkan',
      save: 'Simpan',
      delete: 'Padam',
      edit: 'Edit',
      view: 'Lihat',
      back: 'Kembali',
      next: 'Seterusnya',
      previous: 'Sebelumnya',
      search: 'Cari',
      filter: 'Tapis',
      sort: 'Susun',
      more: 'Lagi'
    },

    // 导航
    nav: {
      home: 'Laman Utama',
      allCoin: 'Semua Syiling',
      auction: 'Lelongan',
      premium: 'Premium',
      buyNow: 'Beli Sekarang',
      sportsCards: 'Syiling Peringatan',
      tradingCards: 'Wang Kertas Vintaj',
      valuation: 'Penilaian',
      admin: 'Admin',
      about: 'Tentang Kami'
    },

    // Header
    header: {
      searchPlaceholder: 'Cari syiling, wang kertas, dll...',
      menu: 'Menu'
    },

    // 公司简介页面
    about: {
      title: 'Tentang Kami',
      subtitle: 'myduitlama adalah platform yang memfokuskan kepada pengumpulan, perdagangan dan lelongan syiling purba dan wang kertas lama dari Malaysia dan negara-negara di seluruh dunia. Kami komited untuk menyediakan koleksi dan perkhidmatan yang tulen, profesional dan berkualiti tinggi kepada setiap pengumpul dan pelabur.',
      aboutUsTitle: 'Tentang Kami',
      aboutUsContent1: 'Kami pakar dalam pemerolehan, penjualan dan lelongan wang kertas lama dan syiling, sambil menyediakan perkhidmatan perundingan untuk penggredan berwibawa antarabangsa (seperti PMG, PCGS), dan boleh menugaskan lelongan koleksi bernilai tinggi bagi pihak pelanggan.',
      aboutUsContent2: 'Kami mempunyai saluran perdagangan dan lelongan yang meliputi China, Amerika Syarikat, United Kingdom, Jepun dan negara-negara utama lain, dan komited untuk berkongsi pengetahuan pengumpulan dan keadaan pasaran untuk membantu pelanggan membuat keputusan yang lebih termaklum dalam pengumpulan dan pelaburan.',
      professionalPlatform: 'Platform Koleksi Profesional',
      trustedExpert: 'Pakar Perdagangan Syiling Purba dan Wang Kertas Lama yang Dipercayai',
      contactUs: 'Hubungi Kami',
      contactInfo: 'Maklumat Hubungan',
      email: 'E-mel',
      phone: 'Telefon'
    },

    // 侧边栏
    sidebar: {
      categories: 'Kategori Syiling',
      allCoin: 'Semua Syiling'
    },

    // 分类翻译
    category: {
      '入门': 'Tahap Permulaan',
      '高端': 'Tahap Tinggi',
      '收藏级': 'Gred Koleksi',
      '纪念币': 'Syiling Peringatan',
      '古典纸币': 'Wang Kertas Klasik',
      '体育卡牌': 'Kad Sukan',
      '游戏卡牌': 'Kad Permainan'
    },

    // 首页
    home: {
      title: 'Syiling & Wang Kertas Pilihan',
      subtitle: 'Syiling dan wang kertas koleksi bernilai tinggi yang dipilih dengan status dagangan masa nyata',
      buyNowTab: 'Beli Sekarang',
      valuationTab: 'Penilaian',
      buyNowMode: 'Harga Beli Sekarang',
      valuationMode: 'Harga Penilaian',
      buyNowDesc: 'Harga pembelian semasa yang tersedia',
      valuationDesc: 'Penilaian profesional berdasarkan data pasaran',
      viewDetails: 'Lihat Butiran',
      startValuation: 'Mula Penilaian',
      buyNow: 'Beli Sekarang',
      makeOffer: 'Buat Tawaran',
      heroTitle1: 'Koleksi Syiling Jarang',
      heroSubtitle1: 'Siri Mata Wang Bersejarah',
      heroTitle2: 'Wang Kertas Vintaj',
      heroSubtitle2: 'Koleksi Edisi Terhad',
      heroTitle3: 'Syiling Peringatan',
      heroSubtitle3: 'Siri Terbitan Khas',
      hotRecommendation: 'Cadangan Popular',
      weeklyPopular: 'Kategori syiling paling popular minggu ini'
    },

    // 钱币相关
    coin: {
      name: 'Nama Syiling',
      year: 'Tahun',
      grade: 'Gred',
      price: 'Harga',
      originalPrice: 'Harga Asal',
      estimatedValue: 'Nilai Anggaran',
      status: 'Status',
      endTime: 'Masa Tamat',
      seller: 'Penjual',
      condition: 'Keadaan',
      rarity: 'Kekurangan',
      category: 'Kategori',
      description: 'Penerangan',
      images: 'Gambar',
      auctioning: 'Dalam Lelongan',
      buyNowStatus: 'Beli Sekarang',
      available: 'Tersedia untuk Penilaian',
      noReserve: 'Tiada Rizab',
      details: 'Butiran Kad',
      loading: 'Memuatkan...',
      invalidId: 'ID item tidak sah',
      notFound: 'Item tidak dijumpai',
      loadError: 'Gagal memuatkan maklumat item',
      sortBy: 'Susun mengikut',
      sortFeatured: 'Pilihan',
      sortPriceHigh: 'Harga: Tinggi ke Rendah',
      sortPriceLow: 'Harga: Rendah ke Tinggi',
      sortNewest: 'Terbaru',
      priceType: 'Jenis Harga',
      purchasePrice: 'Harga Pembelian',
      estimatedPrice: 'Harga Anggaran',
      loadingMore: 'Memuatkan lagi...',
      noMoreCoins: 'Tiada lagi syiling',
      valuation: 'Penilaian',
      buyNow: 'Beli Sekarang',
      higherThan: 'Lebih tinggi daripada',
      lowerThan: 'Lebih rendah daripada',
      purchasePrice: 'Harga Pembelian',
      priceBasedOnMarket: 'Harga berdasarkan keadaan pasaran semasa, dikemas kini setiap hari',
      clickToView: 'Klik untuk melihat gambar yang lebih besar',
      loading: 'Memuatkan...',
      loadingInfo: 'Memuatkan maklumat koleksi...',
      qualityAssurance: 'Jaminan Kualiti',
      qualityDescription: 'Semua wang kertas lama telah melalui pensijilan gred profesional untuk memastikan kualiti dan keaslian. Kami berjanji bahawa setiap syiling atau wang kertas melalui pemeriksaan kualiti yang ketat untuk memberikan anda pengalaman mengumpul yang paling boleh dipercayai.',
      qualityGuarantee: 'Jaminan Kualiti',
      strictQualityStandards: 'Standard pemeriksaan kualiti yang ketat',
      professionalService: 'Perkhidmatan Profesional',
      itemDescription: 'Penerangan Item',
      noDescription: 'Tiada penerangan tersedia',
      collectionAdvice: 'Nasihat Koleksi',
      collectionTip: 'Syiling atau wang kertas ini adalah koleksi yang jarang ditemui. Disyorkan untuk menyimpannya dengan betul, mengelakkan cahaya matahari langsung dan persekitaran lembap.',
      detailedSpecs: 'Spesifikasi Terperinci',
      noSpecsInfo: 'Tiada maklumat spesifikasi terperinci tersedia',
      issueYear: 'Tahun Terbitan',
      unknown: 'Tidak Diketahui',
      gradeLevel: 'Tahap Gred',
      grading: 'Dalam Penggredan',
      status: 'Status',
      conditionDesc: 'Penerangan Keadaan',
      excellent: 'Cemerlang',
      afterSalesService: 'Perkhidmatan Selepas Jualan',
      professionalSupport: 'Sokongan pasukan khidmat pelanggan profesional',
      professionalCertification: 'Pensijilan Profesional',
      authorityCertification: 'Pensijilan gred institusi berwibawa',
      backToHome: 'Kembali ke Laman Utama',
      itemDetails: 'Butiran Item',
      leftImageArea: 'Kawasan Gambar',
      rightInfoArea: 'Kawasan Maklumat Produk',
      topTitleFavorite: 'Tajuk dan Butang Kegemaran',
      statusLabel: 'Label Status',
      middlePriceInfo: 'Maklumat Harga',
      save: 'Jimat',
      additionalProductInfo: 'Maklumat Produk Tambahan',
      bottomActionButtons: 'Butang Tindakan',
      valuationPageLink: 'Pautan Halaman Penilaian',
      qualityAssuranceBlock: 'Blok Jaminan Kualiti',
      guaranteeFeatures: 'Ciri-ciri Jaminan',
      detailedInfo: 'Maklumat Terperinci'
    },

    // 时间相关
    time: {
      days: 'Hari',
      day: 'hari',
      hours: 'Jam',
      minutes: 'Min',
      seconds: 'Saat',
      expired: 'Tamat',
      expiringSoon: 'Penilaian akan tamat tidak lama lagi, sila proses dengan segera',
      endsIn: 'Berakhir dalam',
      hoursLeft: 'jam lagi',
      dayLeft: 'hari lagi',
      daysLeft: 'hari lagi',
      ended: 'Tamat'
    },
    
    // 估值页面
    valuation: {
      title: 'Halaman Penilaian',
      buyPrice: 'Harga Beli',
      sellPrice: 'Harga Jual',
      remainingTime: 'Masa Berbaki',
      duration: 'Tempoh Jualan',
      expectedPrice: 'Harga Jualan Dijangka',
      expectedProfit: 'Keuntungan Dijangka',
      customPrice: 'Harga Beli Tersuai',
      fixedPrice: 'Harga Tetap',
      fixedPriceSection: 'Harga Tetap',
      fixedPriceSetting: 'Tetapan Harga Tetap',
      estimatedSellPrice: 'Harga Jual Anggaran',
      estimatedSellPriceDesc: 'Harga jual anggaran berdasarkan harga tetap dan pekali jualan',
      estimatedProfit: 'Keuntungan Anggaran',
      expectedReturn: 'Pulangan Dijangka',
      higherReturn: 'Pulangan Lebih Tinggi',
      priceOptions: 'Pilihan Harga',
      oneDayOption: 'Jualan 1 Hari (Pulangan Stabil)',
      twoDayOption: 'Jualan 2 Hari (Potensi Lebih Tinggi)',
      startValuation: 'Mula Penilaian',
      valuationExpired: 'Penilaian Tamat Tempoh',
      valuationExpiring: 'Penilaian akan tamat tidak lama lagi, sila proses dengan segera',
      includesFees: 'Termasuk Yuran',
      usageGuide: 'Panduan Penggunaan',
      buyPriceGuide: 'Tetapan Harga Beli',
      buyPriceDesc: 'Tetapkan harga maksimum yang anda sanggup bayar untuk syiling ini. Sistem akan memadankan anda dengan harga terbaik berdasarkan keadaan pasaran.',
      sellPriceGuide: 'Anggaran Harga Jualan',
      sellPriceDesc: 'Harga jualan anggaran berdasarkan trend pasaran untuk tempoh masa yang dipilih untuk membantu anda membuat keputusan pelaburan terbaik.',
      // API错误信息
      cardNotFound: 'Kad tidak dijumpai',
      getValuationFailed: 'Gagal mendapatkan maklumat penilaian',
      missingParameters: 'Parameter yang diperlukan hilang',
      submitValuationFailed: 'Gagal menghantar penilaian',
      submitValuationSuccess: 'Penilaian berjaya dihantar'
    },

    // WhatsApp客服
    whatsapp: {
      contactUs: 'Hubungi Kami melalui WhatsApp',
      tooltip: 'Perlukan bantuan? Sembang dengan kami di WhatsApp!',
      defaultMessage: 'Hello! Saya ingin bertanya tentang syiling dan wang kertas anda.'
    }
  }
};

// 当前语言设置
let currentLanguage = 'en'; // 默认英文

// 获取翻译文本
export function t(key, lang) {
  // 如果没有指定语言，使用当前语言
  if (!lang) {
    lang = getCurrentLanguage();
  }

  const keys = key.split('.');
  let value = translations[lang];

  for (const k of keys) {
    if (value && typeof value === 'object') {
      value = value[k];
    } else {
      return key; // 如果找不到翻译，返回原key
    }
  }

  return value || key;
}



// 格式化时间显示
export function formatTimeLeft(timeString, lang) {
  if (!timeString) return '';

  // 如果没有指定语言，使用当前语言
  if (!lang) {
    lang = getCurrentLanguage();
  }

  // 解析时间字符串，支持多种格式
  const match = timeString.match(/(\d+)(小时|天|hour|day|jam|hari)/i);
  if (!match) return timeString; // 如果无法解析，返回原字符串

  const number = parseInt(match[1]);
  const unit = match[2].toLowerCase();

  // 根据语言和单位返回格式化的时间
  if (lang === 'zh') {
    if (unit.includes('小时') || unit.includes('hour') || unit.includes('jam')) {
      return `${number}${t('time.hoursLeft', lang)}`;
    } else if (unit.includes('天') || unit.includes('day') || unit.includes('hari')) {
      return number === 1 ? `${number}${t('time.dayLeft', lang)}` : `${number}${t('time.daysLeft', lang)}`;
    }
  } else if (lang === 'en') {
    if (unit.includes('小时') || unit.includes('hour') || unit.includes('jam')) {
      return `${number} ${t('time.hoursLeft', lang)}`;
    } else if (unit.includes('天') || unit.includes('day') || unit.includes('hari')) {
      return number === 1 ? `${number} ${t('time.dayLeft', lang)}` : `${number} ${t('time.daysLeft', lang)}`;
    }
  } else if (lang === 'ms') {
    if (unit.includes('小时') || unit.includes('hour') || unit.includes('jam')) {
      return `${t('time.endsIn', lang)} ${number} ${t('time.hoursLeft', lang)}`;
    } else if (unit.includes('天') || unit.includes('day') || unit.includes('hari')) {
      return number === 1 ? `${t('time.endsIn', lang)} ${number} ${t('time.dayLeft', lang)}` : `${t('time.endsIn', lang)} ${number} ${t('time.daysLeft', lang)}`;
    }
  }

  return timeString; // 如果无法处理，返回原字符串
}

// 设置语言
export function setLanguage(lang) {
  if (translations[lang]) {
    currentLanguage = lang;
    // 可以在这里添加本地存储
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', lang);
    }
  }
}

// 获取当前语言
export function getCurrentLanguage() {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('language');
    if (stored && translations[stored]) {
      currentLanguage = stored;
    }
  }
  return currentLanguage;
}

// 获取所有可用语言
export function getAvailableLanguages() {
  return Object.keys(translations);
}

// 获取分类名称的翻译
export function getCategoryName(categoryName, lang) {
  // 如果没有指定语言，使用当前语言
  if (!lang) {
    lang = getCurrentLanguage();
  }

  // 尝试从翻译中获取分类名称
  const categoryTranslations = translations[lang]?.category;
  if (categoryTranslations && categoryTranslations[categoryName]) {
    return categoryTranslations[categoryName];
  }

  // 如果没有找到翻译，返回原名称
  return categoryName;
}

// 初始化语言设置
export function initializeLanguage() {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('language');
    if (stored && translations[stored]) {
      currentLanguage = stored;
    } else {
      // 如果没有存储的语言设置，默认设置为英文
      currentLanguage = 'en';
      localStorage.setItem('language', 'en');
    }
  }
}
