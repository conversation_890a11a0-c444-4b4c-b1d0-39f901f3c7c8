import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import Header from '../../components/Header';
import ImageGallery from '../../components/ImageGallery';
import CountdownTimer from '../../components/CountdownTimer';
import { useSiteConfig } from '../../hooks/useSiteConfig';
import { t } from '../../lib/i18n';

export default function ValuationPage() {
  const router = useRouter();
  const { id } = router.query;
  const { siteConfig, loading: configLoading } = useSiteConfig();

  const [card, setCard] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [buyPrice, setBuyPrice] = useState('');
  const [sellPrice, setSellPrice] = useState('');
  const [selectedDuration, setSelectedDuration] = useState('1day');

  useEffect(() => {
    if (id) {
      loadCard();
    }
  }, [id]);

  async function loadCard() {
    try {
      setLoading(true);
      const response = await fetch(`/api/cards/${id}`);

      if (!response.ok) {
        if (response.status === 404) {
          setError(t('common.error'));
        } else {
          setError(t('common.error'));
        }
        return;
      }

      const cardData = await response.json();
      setCard(cardData);

      // 使用固定价格（estimatedValue）和抛售系数计算
      const fixedPrice = cardData.estimatedValue || cardData.price;
      const sellMultiplier = cardData.sellMultiplier || 1.050;

      setBuyPrice(fixedPrice.toFixed(2));
      setSellPrice((fixedPrice * sellMultiplier).toFixed(2));
    } catch (error) {
      console.error('Error loading card:', error);
      setError(t('common.error'));
    } finally {
      setLoading(false);
    }
  }

  const handleSubmitValuation = async () => {
    try {
      const response = await fetch(`/api/valuation/${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          buyPrice: buyPrice,
          sellPrice: sellPrice,
          duration: selectedDuration
        })
      });

      if (response.ok) {
        alert(`${t('common.success')}!\n${t('valuation.buyPrice')}: RM${buyPrice}\n${t('valuation.sellPrice')}: RM${sellPrice}\n${t('valuation.duration')}: ${selectedDuration === '1day' ? t('valuation.oneDayOption') : t('valuation.twoDayOption')}`);
      } else {
        const error = await response.json();
        alert(`${t('common.error')}: ${error.error || t('common.error')}`);
      }
    } catch (error) {
      console.error('Error submitting valuation:', error);
      alert(t('common.error'));
    }
  };

  if (loading || configLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        <Header siteConfig={siteConfig} />
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !card) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        <Header siteConfig={siteConfig} />
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-6xl text-gray-400 mb-4">😞</div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">{error || t('common.error')}</h2>
              <Link href="/">
                <a className="text-blue-600 hover:text-blue-800 transition-colors duration-200">
                  {t('common.back')}
                </a>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>{card.name} - {t('nav.valuation')} - {siteConfig.title}</title>
        <meta name="description" content={`${t('valuation.title')} - ${card.name}`} />
      </Head>

      <div className="min-h-screen bg-gray-50">
        <Header siteConfig={siteConfig} />
        
        <div className="max-w-sm mx-auto px-4 py-4 sm:max-w-2xl sm:px-6 sm:py-6 lg:max-w-7xl lg:px-8 lg:py-8 xl:max-w-[1600px] xl:px-12 2xl:max-w-[1800px] 2xl:px-16">
          {/* 导航面包屑 */}
          <nav className="flex items-center space-x-2 text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6 lg:mb-8 overflow-x-auto">
            <Link href="/">
              <a className="hover:text-purple-600 transition-colors duration-200 whitespace-nowrap">{t('nav.home')}</a>
            </Link>
            <div className="w-3 h-3 sm:w-4 sm:h-4 flex items-center justify-center flex-shrink-0">
              <i className="ri-arrow-right-s-line"></i>
            </div>
            <Link href={`/card/${card.id}`}>
              <a className="hover:text-purple-600 transition-colors duration-200 whitespace-nowrap">{t('coin.details')}</a>
            </Link>
            <div className="w-3 h-3 sm:w-4 sm:h-4 flex items-center justify-center flex-shrink-0">
              <i className="ri-arrow-right-s-line"></i>
            </div>
            <span className="text-purple-600 font-medium whitespace-nowrap">{t('nav.valuation')}</span>
          </nav>

          {/* 主要内容区域 - 调整后的两列布局 */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8">
            {/* 左侧：卡牌展示 - 占2列（增加20%宽度） */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 sticky top-8">
                {/* 卡牌图片展示 */}
                <div className="relative">
                  <div className="aspect-square bg-gray-100 p-4 sm:p-6 lg:p-8">
                    <div className="w-full h-full rounded-lg overflow-hidden">
                      <ImageGallery images={card.images} />
                    </div>
                  </div>
                  {/* 浮动标签 */}
                  <div className="absolute top-3 right-3 bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium">
                    {t('nav.valuation')}
                  </div>
                </div>

                {/* 卡牌信息区域 */}
                <div className="p-4 sm:p-6">
                  
                  {/* 状态信息卡片 */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium text-gray-700">{t('coin.status')}</span>
                      </div>
                      <span className="text-sm font-medium text-green-600 bg-green-100 px-2 py-1 rounded-md">
                        {t('coin.available')}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{t('valuation.remainingTime')}</span>
                      <span className="text-sm font-medium text-gray-900 bg-white px-2 py-1 rounded-md border">
                        1 {t('time.day')}
                      </span>
                    </div>
                  </div>
                  
                  {/* 卡牌规格信息 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white rounded-lg p-4 text-center border border-gray-200">
                      <div className="text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium">{t('coin.year')}</div>
                      <div className="text-lg font-semibold text-gray-900">{card.year}</div>
                    </div>
                    <div className="bg-white rounded-lg p-4 text-center border border-gray-200">
                      <div className="text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium">{t('coin.grade')}</div>
                      <div className="text-lg font-semibold text-gray-900">{card.grade}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：合并的买入价格设置和抛售价格操作 - 占3列 */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 flex flex-col">
                {/* 卡牌标题区域 */}
                <div className="px-6 py-4 border-b border-gray-200">
                  <h1 className="text-2xl font-bold text-gray-900 mb-1">{card.name}</h1>
                  <p className="text-gray-600 text-sm">{card.subtitle}</p>
                </div>

                {/* 固定价格区域 */}
                <div className="bg-gray-900 px-6 py-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-lg font-semibold text-gray-200">{t('valuation.fixedPriceSection')}</h2>
                      <div className="text-2xl font-bold mt-1">
                        RM{parseFloat(buyPrice).toLocaleString()}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-400">{t('valuation.includesFees')}</div>
                      <div className="text-sm text-gray-300">(BP)</div>
                    </div>
                  </div>
                </div>

                {/* 剩余时间区域 */}
                <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                  <div className="text-xs text-red-600 mb-2 font-medium">{t('valuation.remainingTime')}</div>
                  <CountdownTimer
                    duration={selectedDuration === '1day' ? 24 : 48}
                    onComplete={() => alert(t('valuation.valuationExpired'))}
                  />
                </div>

                {/* 内容区域 */}
                <div className="p-6">
                  {/* 固定价格设置区域 */}
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-gray-700 mb-3">{t('valuation.fixedPriceSetting')}</h3>

                    {/* 固定价格显示 */}
                    <div className="mb-4">
                      <label className="block text-xs font-medium text-gray-700 mb-2">{t('valuation.fixedPrice')}</label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">RM</span>
                        <input
                          type="number"
                          value={buyPrice}
                          readOnly
                          className="w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-900 bg-gray-50 cursor-not-allowed transition-all duration-200"
                          placeholder="1,590.00"
                        />
                        <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors">
                          <div className="w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center">
                            <span className="text-xs">?</span>
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* 分隔线 */}
                  <div className="border-t border-gray-200 my-4"></div>

                  {/* 预估抛售价格区域 */}
                  <div>
                    <div className="mb-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">{t('valuation.estimatedSellPrice')}</h3>
                      <p className="text-gray-600 text-xs">{t('valuation.estimatedSellPriceDesc')}</p>
                    </div>

                    <div>
                      {/* 期限选择 */}
                      <div className="mb-4">
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          {t('valuation.duration')}
                        </label>
                        <select
                          value={selectedDuration}
                          onChange={(e) => setSelectedDuration(e.target.value)}
                          className="w-full p-2.5 border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-900 focus:border-gray-900 text-sm bg-white transition-all duration-200"
                        >
                          <option value="1day">{t('valuation.oneDayOption')}</option>
                          <option value="2day">{t('valuation.twoDayOption')}</option>
                        </select>
                      </div>

                      {/* 预估抛售价格显示 - 突出显示 */}
                      <div className="bg-green-50 rounded-lg p-4 mb-4 border border-green-200">
                        <div className="text-center">
                          <div className="text-xs text-green-700 font-medium mb-1">{t('valuation.estimatedSellPrice')}</div>
                          <div className="text-2xl font-bold text-green-800 mb-1">
                            RM{parseFloat(sellPrice).toLocaleString()}
                          </div>
                          <div className="text-xs text-green-600">
                            {t('valuation.estimatedProfit')}: +RM{parseFloat(sellPrice - buyPrice).toLocaleString()}
                          </div>
                        </div>
                      </div>

                      {/* 开始按钮 */}
                      <div>
                        <button
                          onClick={handleSubmitValuation}
                          className="w-full bg-gray-900 hover:bg-gray-800 text-white py-3 px-4 rounded-md font-medium text-sm transition-all duration-200"
                        >
                          {t('valuation.startValuation')}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 底部提示信息 - 跨越所有列 */}
          <div className="mt-8">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="max-w-4xl mx-auto">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">{t('valuation.usageGuide')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-lg">💰</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">{t('valuation.buyPriceGuide')}</h4>
                      <p className="text-sm text-gray-600">{t('valuation.buyPriceDesc')}</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-lg">📈</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">{t('valuation.sellPriceGuide')}</h4>
                      <p className="text-sm text-gray-600">{t('valuation.sellPriceDesc')}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
