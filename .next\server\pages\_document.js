/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./lib/i18n.js":
/*!*********************!*\
  !*** ./lib/i18n.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"formatTimeLeft\": () => (/* binding */ formatTimeLeft),\n/* harmony export */   \"getAvailableLanguages\": () => (/* binding */ getAvailableLanguages),\n/* harmony export */   \"getCategoryName\": () => (/* binding */ getCategoryName),\n/* harmony export */   \"getCurrentLanguage\": () => (/* binding */ getCurrentLanguage),\n/* harmony export */   \"initializeLanguage\": () => (/* binding */ initializeLanguage),\n/* harmony export */   \"setLanguage\": () => (/* binding */ setLanguage),\n/* harmony export */   \"t\": () => (/* binding */ t),\n/* harmony export */   \"translations\": () => (/* binding */ translations)\n/* harmony export */ });\n// 国际化配置文件\nconst translations = {\n    en: {\n        // 通用\n        common: {\n            loading: \"Loading...\",\n            error: \"Error\",\n            success: \"Success\",\n            cancel: \"Cancel\",\n            confirm: \"Confirm\",\n            save: \"Save\",\n            delete: \"Delete\",\n            edit: \"Edit\",\n            view: \"View\",\n            back: \"Back\",\n            next: \"Next\",\n            previous: \"Previous\",\n            search: \"Search\",\n            filter: \"Filter\",\n            sort: \"Sort\",\n            more: \"More\"\n        },\n        // 导航\n        nav: {\n            home: \"Home\",\n            allCoin: \"All Coin\",\n            auction: \"Auction\",\n            premium: \"Premium\",\n            buyNow: \"Buy Now\",\n            sportsCards: \"Commemorative Coins\",\n            tradingCards: \"Vintage Banknotes\",\n            valuation: \"Valuation\",\n            admin: \"Admin\",\n            about: \"About Us\"\n        },\n        // Header\n        header: {\n            searchPlaceholder: \"Search coins, banknotes, etc...\",\n            menu: \"Menu\"\n        },\n        // 公司简介页面\n        about: {\n            title: \"About Us\",\n            subtitle: \"myduitlama is a platform focused on collecting, trading and auctioning ancient coins and old banknotes from Malaysia and countries around the world. We are committed to providing authentic, professional and high-quality collectibles and services to every collector and investor.\",\n            aboutUsTitle: \"About Us\",\n            aboutUsContent1: \"We specialize in the acquisition, sale and auction of old banknotes and coins, while providing consulting services for international authoritative grading (such as PMG, PCGS), and can commission the auction of high-value collections on behalf of clients.\",\n            aboutUsContent2: \"We have trading and auction channels covering China, the United States, the United Kingdom, Japan and other major countries, and are committed to sharing collecting knowledge and market conditions to help clients make more informed decisions in collecting and investing.\",\n            professionalPlatform: \"Professional Collection Platform\",\n            trustedExpert: \"Trusted Ancient Coin and Banknote Trading Expert\",\n            contactUs: \"Contact Us\",\n            contactInfo: \"Contact Information\",\n            email: \"Email\",\n            phone: \"Phone\"\n        },\n        // 侧边栏\n        sidebar: {\n            categories: \"Coin Categories\",\n            allCoin: \"All Coin\"\n        },\n        // 分类翻译\n        category: {\n            \"入门\": \"Entry Level\",\n            \"高端\": \"High End\",\n            \"收藏级\": \"Collector Grade\",\n            \"纪念币\": \"Commemorative Coins\",\n            \"古典纸币\": \"Vintage Banknotes\",\n            \"体育卡牌\": \"Sports Cards\",\n            \"游戏卡牌\": \"Trading Cards\"\n        },\n        // 首页\n        home: {\n            title: \"Featured Coins & Banknotes\",\n            subtitle: \"Curated high-value collectible coins and banknotes with real-time trading status\",\n            buyNowTab: \"Buy Now\",\n            valuationTab: \"Valuation\",\n            buyNowMode: \"Buy Now Prices\",\n            valuationMode: \"Valuation Prices\",\n            buyNowDesc: \"Current available purchase prices\",\n            valuationDesc: \"Professional valuations based on market data\",\n            viewDetails: \"View Details\",\n            startValuation: \"Start Valuation\",\n            buyNow: \"Buy Now\",\n            makeOffer: \"Make Offer\",\n            heroTitle1: \"Rare Coin Collection\",\n            heroSubtitle1: \"Historical Currency Series\",\n            heroTitle2: \"Vintage Banknotes\",\n            heroSubtitle2: \"Limited Edition Collection\",\n            heroTitle3: \"Commemorative Coins\",\n            heroSubtitle3: \"Special Issue Series\",\n            hotRecommendation: \"Hot Recommendation\",\n            weeklyPopular: \"Most popular coin categories this week\"\n        },\n        // 钱币相关\n        coin: {\n            name: \"Coin Name\",\n            year: \"Year\",\n            grade: \"Grade\",\n            price: \"Price\",\n            originalPrice: \"Original Price\",\n            estimatedValue: \"Estimated Value\",\n            purchasePrice: \"Purchase Price\",\n            status: \"Status\",\n            endTime: \"End Time\",\n            seller: \"Seller\",\n            condition: \"Condition\",\n            rarity: \"Rarity\",\n            category: \"Category\",\n            description: \"Description\",\n            images: \"Images\",\n            auctioning: \"Auctioning\",\n            buyNowStatus: \"Buy Now\",\n            available: \"Available\",\n            noReserve: \"No Reserve\",\n            details: \"Details\",\n            loading: \"Loading...\",\n            invalidId: \"Invalid item ID\",\n            notFound: \"Item not found\",\n            loadError: \"Failed to load item information\",\n            sortBy: \"Sort by\",\n            sortFeatured: \"Featured\",\n            sortPriceHigh: \"Price: High to Low\",\n            sortPriceLow: \"Price: Low to High\",\n            sortNewest: \"Newest\",\n            priceType: \"Price Type\",\n            estimatedPrice: \"Estimated Price\",\n            loadingMore: \"Loading more...\",\n            noMoreCoins: \"No more coins\",\n            valuation: \"Valuation\",\n            buyNow: \"Buy Now\",\n            higherThan: \"Higher than\",\n            lowerThan: \"Lower than\",\n            purchasePrice: \"Purchase Price\",\n            priceBasedOnMarket: \"Prices based on current market conditions, updated daily\",\n            clickToView: \"Click to view larger image\",\n            loading: \"Loading...\",\n            loadingInfo: \"Loading collection information...\",\n            qualityAssurance: \"Quality Assurance\",\n            qualityDescription: \"All banknotes are professionally graded and certified to ensure quality and authenticity. We promise that every coin or banknote undergoes strict quality inspection to provide you with the most reliable collecting experience.\",\n            qualityGuarantee: \"Quality Guarantee\",\n            strictQualityStandards: \"Strict quality inspection standards\",\n            professionalService: \"Professional Service\",\n            itemDescription: \"Item Description\",\n            noDescription: \"No description available\",\n            collectionAdvice: \"Collection Advice\",\n            collectionTip: \"This coin or banknote is a rare collectible. It is recommended to store it properly, avoiding direct sunlight and humid environments.\",\n            detailedSpecs: \"Detailed Specifications\",\n            noSpecsInfo: \"No detailed specification information available\",\n            issueYear: \"Issue Year\",\n            unknown: \"Unknown\",\n            gradeLevel: \"Grade Level\",\n            grading: \"Grading\",\n            status: \"Status\",\n            conditionDesc: \"Condition Description\",\n            excellent: \"Excellent\",\n            afterSalesService: \"After-sales Service\",\n            professionalSupport: \"Professional customer service team support\",\n            professionalCertification: \"Professional Certification\",\n            authorityCertification: \"Authoritative institution grading certification\",\n            backToHome: \"Back to Home\",\n            itemDetails: \"Item Details\",\n            leftImageArea: \"Image Area\",\n            rightInfoArea: \"Product Information Area\",\n            topTitleFavorite: \"Title and Favorite Button\",\n            statusLabel: \"Status Label\",\n            middlePriceInfo: \"Price Information\",\n            save: \"Save\",\n            additionalProductInfo: \"Additional Product Information\",\n            bottomActionButtons: \"Action Buttons\",\n            valuationPageLink: \"Valuation Page Link\",\n            qualityAssuranceBlock: \"Quality Assurance Block\",\n            guaranteeFeatures: \"Guarantee Features\",\n            detailedInfo: \"Detailed Information\"\n        },\n        // 时间相关\n        time: {\n            days: \"Days\",\n            day: \"day\",\n            hours: \"Hours\",\n            minutes: \"Min\",\n            seconds: \"Sec\",\n            expired: \"Expired\",\n            expiringSoon: \"Valuation expiring soon, please process in time\",\n            endsIn: \"Ends in\",\n            hoursLeft: \"hours left\",\n            dayLeft: \"day left\",\n            daysLeft: \"days left\",\n            ended: \"Ended\"\n        },\n        // 估值页面\n        valuation: {\n            title: \"Valuation\",\n            buyPrice: \"Buy Price\",\n            sellPrice: \"Sell Price\",\n            remainingTime: \"Time Remaining\",\n            duration: \"Sell Duration\",\n            expectedPrice: \"Expected Sell Price\",\n            expectedProfit: \"Expected Profit\",\n            customPrice: \"Custom Buy Price\",\n            fixedPrice: \"Fixed Price\",\n            fixedPriceSection: \"Fixed Price\",\n            fixedPriceSetting: \"Fixed Price Setting\",\n            estimatedSellPrice: \"Estimated Sell Price\",\n            estimatedSellPriceDesc: \"Estimated sell price based on fixed price and sell multiplier\",\n            estimatedProfit: \"Estimated Profit\",\n            expectedReturn: \"Expected Return\",\n            higherReturn: \"Higher Returns\",\n            priceOptions: \"Price Options\",\n            oneDayOption: \"1 Day Sale (Stable Returns)\",\n            twoDayOption: \"2 Day Sale (Higher Potential)\",\n            startValuation: \"Start Valuation\",\n            valuationExpired: \"Valuation Expired\",\n            valuationExpiring: \"Valuation expiring soon, please process in time\",\n            includesFees: \"Includes Fees\",\n            usageGuide: \"Usage Guide\",\n            buyPriceGuide: \"Buy Price Setting\",\n            buyPriceDesc: \"Set the maximum price you are willing to pay for this coin. The system will match you with the best available price based on market conditions.\",\n            sellPriceGuide: \"Sell Price Estimation\",\n            sellPriceDesc: \"Estimated selling price based on market trends for the selected time period to help you make the best investment decisions.\",\n            // API错误信息\n            cardNotFound: \"Card not found\",\n            getValuationFailed: \"Failed to get valuation information\",\n            missingParameters: \"Missing required parameters\",\n            submitValuationFailed: \"Failed to submit valuation\",\n            submitValuationSuccess: \"Valuation submitted successfully\"\n        },\n        // WhatsApp客服\n        whatsapp: {\n            contactUs: \"Contact Us via WhatsApp\",\n            tooltip: \"Need help? Chat with us on WhatsApp!\",\n            defaultMessage: \"Hello! I would like to inquire about your coins and banknotes.\"\n        }\n    },\n    zh: {\n        // 通用\n        common: {\n            loading: \"加载中...\",\n            error: \"错误\",\n            success: \"成功\",\n            cancel: \"取消\",\n            confirm: \"确认\",\n            save: \"保存\",\n            delete: \"删除\",\n            edit: \"编辑\",\n            view: \"查看\",\n            back: \"返回\",\n            next: \"下一步\",\n            previous: \"上一步\",\n            search: \"搜索\",\n            filter: \"筛选\",\n            sort: \"排序\",\n            more: \"更多\"\n        },\n        // 导航\n        nav: {\n            home: \"首页\",\n            allCoin: \"所有古币\",\n            auction: \"拍卖\",\n            premium: \"精品\",\n            buyNow: \"立即购买\",\n            sportsCards: \"纪念币\",\n            tradingCards: \"古典纸币\",\n            valuation: \"估值功能\",\n            admin: \"管理后台\",\n            about: \"公司简介\"\n        },\n        // Header\n        header: {\n            searchPlaceholder: \"搜索古币、纸币等...\",\n            menu: \"菜单\"\n        },\n        // 公司简介页面\n        about: {\n            title: \"公司简介\",\n            subtitle: \"myduitlama 是一家专注于马来西亚及世界各国古钱币、旧纸币收藏、交易与拍卖的平台。我们致力于为每一位收藏爱好者和投资者，提供真实、专业、高品质的收藏品和服务。\",\n            aboutUsTitle: \"关于我们\",\n            aboutUsContent1: \"我们专注于旧纸币与硬币的收购、销售与拍卖，同时提供国际权威评级（如 PMG、PCGS）的咨询服务，并可代客户委托拍卖高价值藏品。\",\n            aboutUsContent2: \"我们拥有覆盖中国、美国、英国、日本及其他主要国家的买卖与拍卖交易渠道，致力于分享收藏知识与市场行情，帮助客户在收藏与投资中做出更明智的决策。\",\n            professionalPlatform: \"专业收藏平台\",\n            trustedExpert: \"值得信赖的古币旧钞交易专家\",\n            contactUs: \"联系我们\",\n            contactInfo: \"联系方式\",\n            email: \"邮箱\",\n            phone: \"电话\"\n        },\n        // 侧边栏\n        sidebar: {\n            categories: \"古币分类\",\n            allCoin: \"所有古币\"\n        },\n        // 分类翻译\n        category: {\n            \"入门\": \"入门\",\n            \"高端\": \"高端\",\n            \"收藏级\": \"收藏级\",\n            \"纪念币\": \"纪念币\",\n            \"古典纸币\": \"古典纸币\",\n            \"体育卡牌\": \"体育卡牌\",\n            \"游戏卡牌\": \"游戏卡牌\"\n        },\n        // 首页\n        home: {\n            title: \"精选古币纸币\",\n            subtitle: \"精选高价值收藏古币纸币，实时交易状态\",\n            buyNowTab: \"立即购买\",\n            valuationTab: \"估值功能\",\n            buyNowMode: \"购买价格\",\n            valuationMode: \"估值价格\",\n            buyNowDesc: \"当前可购买价格\",\n            valuationDesc: \"基于市场数据的专业估值\",\n            viewDetails: \"查看详情\",\n            startValuation: \"开始估值\",\n            buyNow: \"立即购买\",\n            makeOffer: \"出价\",\n            heroTitle1: \"稀有古币收藏\",\n            heroSubtitle1: \"历史货币系列\",\n            heroTitle2: \"古典纸币\",\n            heroSubtitle2: \"限量版收藏\",\n            heroTitle3: \"纪念币系列\",\n            heroSubtitle3: \"特别发行版\",\n            hotRecommendation: \"热门推荐\",\n            weeklyPopular: \"本周最受欢迎的钱币类别\"\n        },\n        // 钱币相关\n        coin: {\n            name: \"钱币名称\",\n            year: \"年份\",\n            grade: \"评级\",\n            price: \"价格\",\n            originalPrice: \"原价\",\n            estimatedValue: \"市场估值\",\n            purchasePrice: \"购买价格\",\n            status: \"状态\",\n            endTime: \"结束时间\",\n            seller: \"卖家\",\n            condition: \"品相\",\n            rarity: \"稀有度\",\n            category: \"分类\",\n            description: \"描述\",\n            images: \"图片\",\n            auctioning: \"竞拍中\",\n            buyNowStatus: \"立即购买\",\n            available: \"可购买\",\n            noReserve: \"无底价\",\n            details: \"商品详情\",\n            loading: \"加载中...\",\n            invalidId: \"无效的物品ID\",\n            notFound: \"未找到物品\",\n            loadError: \"加载物品信息失败\",\n            sortBy: \"排序方式\",\n            sortFeatured: \"推荐\",\n            sortPriceHigh: \"价格：从高到低\",\n            sortPriceLow: \"价格：从低到高\",\n            sortNewest: \"最新\",\n            priceType: \"价格类型\",\n            estimatedPrice: \"估值价格\",\n            loadingMore: \"加载更多...\",\n            noMoreCoins: \"没有更多钱币了\",\n            valuation: \"估值\",\n            buyNow: \"立即购买\",\n            higherThan: \"高于\",\n            lowerThan: \"低于\",\n            purchasePrice: \"购买价\",\n            priceBasedOnMarket: \"价格基于当前市场行情，每日更新\",\n            clickToView: \"点击查看大图\",\n            loading: \"加载中...\",\n            loadingInfo: \"加载藏品信息中...\",\n            qualityAssurance: \"品质保证\",\n            qualityDescription: \"所有旧钞均经过专业评级认证，确保品质和真实性。我们承诺每一枚古币或旧钞都经过严格的质量检验，为您提供最可靠的收藏体验。\",\n            qualityGuarantee: \"品质保障\",\n            strictQualityStandards: \"严格质量检验标准\",\n            professionalService: \"专业服务\",\n            itemDescription: \"藏品描述\",\n            noDescription: \"暂无描述信息\",\n            collectionAdvice: \"收藏建议\",\n            collectionTip: \"此古币或旧钞属于稀有收藏品，建议妥善保存，避免阳光直射和潮湿环境。\",\n            detailedSpecs: \"详细规格\",\n            noSpecsInfo: \"暂无详细规格信息\",\n            issueYear: \"发行年份\",\n            unknown: \"未知\",\n            gradeLevel: \"品相等级\",\n            grading: \"评级中\",\n            status: \"状态\",\n            conditionDesc: \"品相描述\",\n            excellent: \"优秀\",\n            afterSalesService: \"售后服务\",\n            professionalSupport: \"专业客服团队支持\",\n            professionalCertification: \"专业认证\",\n            authorityCertification: \"权威机构评级认证\",\n            backToHome: \"返回首页\",\n            itemDetails: \"藏品详情\",\n            leftImageArea: \"左侧：图片区域\",\n            rightInfoArea: \"右侧：商品信息区域\",\n            topTitleFavorite: \"顶部：标题和收藏按钮\",\n            statusLabel: \"状态标签\",\n            middlePriceInfo: \"中间：价格信息 - 使用flex-1让这部分占据剩余空间\",\n            save: \"节省\",\n            additionalProductInfo: \"添加一些额外的商品信息来填充空间\",\n            bottomActionButtons: \"底部：操作按钮\",\n            valuationPageLink: \"估值页面链接\",\n            qualityAssuranceBlock: \"品质保证区块 - 单独一行\",\n            guaranteeFeatures: \"添加一些保证特性\",\n            detailedInfo: \"详细信息\"\n        },\n        // 时间相关\n        time: {\n            days: \"天\",\n            day: \"天\",\n            hours: \"小时\",\n            minutes: \"分\",\n            seconds: \"秒\",\n            expired: \"已结束\",\n            expiringSoon: \"估值即将到期，请及时处理\",\n            endsIn: \"\",\n            hoursLeft: \"小时后结束\",\n            dayLeft: \"天后结束\",\n            daysLeft: \"天后结束\",\n            ended: \"已结束\"\n        },\n        // 估值页面\n        valuation: {\n            title: \"估值页面\",\n            buyPrice: \"买入价格\",\n            sellPrice: \"抛售价格\",\n            remainingTime: \"剩余时间\",\n            duration: \"抛售期限\",\n            expectedPrice: \"预期抛售价格\",\n            expectedProfit: \"预期收益\",\n            customPrice: \"自定义买入价格\",\n            fixedPrice: \"固定价格\",\n            fixedPriceSection: \"固定价格\",\n            fixedPriceSetting: \"固定价格设置\",\n            estimatedSellPrice: \"预估抛售价格\",\n            estimatedSellPriceDesc: \"基于固定价格和抛售系数计算的预估抛售价格\",\n            estimatedProfit: \"预估利润\",\n            expectedReturn: \"预期收益\",\n            higherReturn: \"收益更高\",\n            priceOptions: \"价格选项\",\n            oneDayOption: \"1天后抛售 (稳健收益)\",\n            twoDayOption: \"2天后抛售 (高收益潜力)\",\n            startValuation: \"开始估值\",\n            valuationExpired: \"估值已过期\",\n            valuationExpiring: \"估值即将过期，请及时处理\",\n            includesFees: \"含手续费\",\n            usageGuide: \"使用指南\",\n            buyPriceGuide: \"买入价格设置\",\n            buyPriceDesc: \"设置您愿意购买此钱币的最高价格，系统会根据市场情况为您匹配最优价格。\",\n            sellPriceGuide: \"抛售价格预估\",\n            sellPriceDesc: \"根据市场趋势预估在选定时间后出售可获得的价格，帮助您做出最佳投资决策。\",\n            // API错误信息\n            cardNotFound: \"卡牌不存在\",\n            getValuationFailed: \"获取估值信息失败\",\n            missingParameters: \"缺少必要参数\",\n            submitValuationFailed: \"提交估值失败\",\n            submitValuationSuccess: \"估值提交成功\"\n        },\n        // WhatsApp客服\n        whatsapp: {\n            contactUs: \"通过WhatsApp联系我们\",\n            tooltip: \"需要帮助？在WhatsApp上与我们聊天！\",\n            defaultMessage: \"您好！我想咨询一下您的古币纸币。\"\n        }\n    },\n    ms: {\n        // 通用\n        common: {\n            loading: \"Memuatkan...\",\n            error: \"Ralat\",\n            success: \"Berjaya\",\n            cancel: \"Batal\",\n            confirm: \"Sahkan\",\n            save: \"Simpan\",\n            delete: \"Padam\",\n            edit: \"Edit\",\n            view: \"Lihat\",\n            back: \"Kembali\",\n            next: \"Seterusnya\",\n            previous: \"Sebelumnya\",\n            search: \"Cari\",\n            filter: \"Tapis\",\n            sort: \"Susun\",\n            more: \"Lagi\"\n        },\n        // 导航\n        nav: {\n            home: \"Laman Utama\",\n            allCoin: \"Semua Syiling\",\n            auction: \"Lelongan\",\n            premium: \"Premium\",\n            buyNow: \"Beli Sekarang\",\n            sportsCards: \"Syiling Peringatan\",\n            tradingCards: \"Wang Kertas Vintaj\",\n            valuation: \"Penilaian\",\n            admin: \"Admin\",\n            about: \"Tentang Kami\"\n        },\n        // Header\n        header: {\n            searchPlaceholder: \"Cari syiling, wang kertas, dll...\",\n            menu: \"Menu\"\n        },\n        // 公司简介页面\n        about: {\n            title: \"Tentang Kami\",\n            subtitle: \"myduitlama adalah platform yang memfokuskan kepada pengumpulan, perdagangan dan lelongan syiling purba dan wang kertas lama dari Malaysia dan negara-negara di seluruh dunia. Kami komited untuk menyediakan koleksi dan perkhidmatan yang tulen, profesional dan berkualiti tinggi kepada setiap pengumpul dan pelabur.\",\n            aboutUsTitle: \"Tentang Kami\",\n            aboutUsContent1: \"Kami pakar dalam pemerolehan, penjualan dan lelongan wang kertas lama dan syiling, sambil menyediakan perkhidmatan perundingan untuk penggredan berwibawa antarabangsa (seperti PMG, PCGS), dan boleh menugaskan lelongan koleksi bernilai tinggi bagi pihak pelanggan.\",\n            aboutUsContent2: \"Kami mempunyai saluran perdagangan dan lelongan yang meliputi China, Amerika Syarikat, United Kingdom, Jepun dan negara-negara utama lain, dan komited untuk berkongsi pengetahuan pengumpulan dan keadaan pasaran untuk membantu pelanggan membuat keputusan yang lebih termaklum dalam pengumpulan dan pelaburan.\",\n            professionalPlatform: \"Platform Koleksi Profesional\",\n            trustedExpert: \"Pakar Perdagangan Syiling Purba dan Wang Kertas Lama yang Dipercayai\",\n            contactUs: \"Hubungi Kami\",\n            contactInfo: \"Maklumat Hubungan\",\n            email: \"E-mel\",\n            phone: \"Telefon\"\n        },\n        // 侧边栏\n        sidebar: {\n            categories: \"Kategori Syiling\",\n            allCoin: \"Semua Syiling\"\n        },\n        // 分类翻译\n        category: {\n            \"入门\": \"Tahap Permulaan\",\n            \"高端\": \"Tahap Tinggi\",\n            \"收藏级\": \"Gred Koleksi\",\n            \"纪念币\": \"Syiling Peringatan\",\n            \"古典纸币\": \"Wang Kertas Klasik\",\n            \"体育卡牌\": \"Kad Sukan\",\n            \"游戏卡牌\": \"Kad Permainan\"\n        },\n        // 首页\n        home: {\n            title: \"Syiling & Wang Kertas Pilihan\",\n            subtitle: \"Syiling dan wang kertas koleksi bernilai tinggi yang dipilih dengan status dagangan masa nyata\",\n            buyNowTab: \"Beli Sekarang\",\n            valuationTab: \"Penilaian\",\n            buyNowMode: \"Harga Beli Sekarang\",\n            valuationMode: \"Harga Penilaian\",\n            buyNowDesc: \"Harga pembelian semasa yang tersedia\",\n            valuationDesc: \"Penilaian profesional berdasarkan data pasaran\",\n            viewDetails: \"Lihat Butiran\",\n            startValuation: \"Mula Penilaian\",\n            buyNow: \"Beli Sekarang\",\n            makeOffer: \"Buat Tawaran\",\n            heroTitle1: \"Koleksi Syiling Jarang\",\n            heroSubtitle1: \"Siri Mata Wang Bersejarah\",\n            heroTitle2: \"Wang Kertas Vintaj\",\n            heroSubtitle2: \"Koleksi Edisi Terhad\",\n            heroTitle3: \"Syiling Peringatan\",\n            heroSubtitle3: \"Siri Terbitan Khas\",\n            hotRecommendation: \"Cadangan Popular\",\n            weeklyPopular: \"Kategori syiling paling popular minggu ini\"\n        },\n        // 钱币相关\n        coin: {\n            name: \"Nama Syiling\",\n            year: \"Tahun\",\n            grade: \"Gred\",\n            price: \"Harga\",\n            originalPrice: \"Harga Asal\",\n            estimatedValue: \"Nilai Anggaran\",\n            status: \"Status\",\n            endTime: \"Masa Tamat\",\n            seller: \"Penjual\",\n            condition: \"Keadaan\",\n            rarity: \"Kekurangan\",\n            category: \"Kategori\",\n            description: \"Penerangan\",\n            images: \"Gambar\",\n            auctioning: \"Dalam Lelongan\",\n            buyNowStatus: \"Beli Sekarang\",\n            available: \"Tersedia untuk Penilaian\",\n            noReserve: \"Tiada Rizab\",\n            details: \"Butiran Kad\",\n            loading: \"Memuatkan...\",\n            invalidId: \"ID item tidak sah\",\n            notFound: \"Item tidak dijumpai\",\n            loadError: \"Gagal memuatkan maklumat item\",\n            sortBy: \"Susun mengikut\",\n            sortFeatured: \"Pilihan\",\n            sortPriceHigh: \"Harga: Tinggi ke Rendah\",\n            sortPriceLow: \"Harga: Rendah ke Tinggi\",\n            sortNewest: \"Terbaru\",\n            priceType: \"Jenis Harga\",\n            purchasePrice: \"Harga Pembelian\",\n            estimatedPrice: \"Harga Anggaran\",\n            loadingMore: \"Memuatkan lagi...\",\n            noMoreCoins: \"Tiada lagi syiling\",\n            valuation: \"Penilaian\",\n            buyNow: \"Beli Sekarang\",\n            higherThan: \"Lebih tinggi daripada\",\n            lowerThan: \"Lebih rendah daripada\",\n            purchasePrice: \"Harga Pembelian\",\n            priceBasedOnMarket: \"Harga berdasarkan keadaan pasaran semasa, dikemas kini setiap hari\",\n            clickToView: \"Klik untuk melihat gambar yang lebih besar\",\n            loading: \"Memuatkan...\",\n            loadingInfo: \"Memuatkan maklumat koleksi...\",\n            qualityAssurance: \"Jaminan Kualiti\",\n            qualityDescription: \"Semua wang kertas lama telah melalui pensijilan gred profesional untuk memastikan kualiti dan keaslian. Kami berjanji bahawa setiap syiling atau wang kertas melalui pemeriksaan kualiti yang ketat untuk memberikan anda pengalaman mengumpul yang paling boleh dipercayai.\",\n            qualityGuarantee: \"Jaminan Kualiti\",\n            strictQualityStandards: \"Standard pemeriksaan kualiti yang ketat\",\n            professionalService: \"Perkhidmatan Profesional\",\n            itemDescription: \"Penerangan Item\",\n            noDescription: \"Tiada penerangan tersedia\",\n            collectionAdvice: \"Nasihat Koleksi\",\n            collectionTip: \"Syiling atau wang kertas ini adalah koleksi yang jarang ditemui. Disyorkan untuk menyimpannya dengan betul, mengelakkan cahaya matahari langsung dan persekitaran lembap.\",\n            detailedSpecs: \"Spesifikasi Terperinci\",\n            noSpecsInfo: \"Tiada maklumat spesifikasi terperinci tersedia\",\n            issueYear: \"Tahun Terbitan\",\n            unknown: \"Tidak Diketahui\",\n            gradeLevel: \"Tahap Gred\",\n            grading: \"Dalam Penggredan\",\n            status: \"Status\",\n            conditionDesc: \"Penerangan Keadaan\",\n            excellent: \"Cemerlang\",\n            afterSalesService: \"Perkhidmatan Selepas Jualan\",\n            professionalSupport: \"Sokongan pasukan khidmat pelanggan profesional\",\n            professionalCertification: \"Pensijilan Profesional\",\n            authorityCertification: \"Pensijilan gred institusi berwibawa\",\n            backToHome: \"Kembali ke Laman Utama\",\n            itemDetails: \"Butiran Item\",\n            leftImageArea: \"Kawasan Gambar\",\n            rightInfoArea: \"Kawasan Maklumat Produk\",\n            topTitleFavorite: \"Tajuk dan Butang Kegemaran\",\n            statusLabel: \"Label Status\",\n            middlePriceInfo: \"Maklumat Harga\",\n            save: \"Jimat\",\n            additionalProductInfo: \"Maklumat Produk Tambahan\",\n            bottomActionButtons: \"Butang Tindakan\",\n            valuationPageLink: \"Pautan Halaman Penilaian\",\n            qualityAssuranceBlock: \"Blok Jaminan Kualiti\",\n            guaranteeFeatures: \"Ciri-ciri Jaminan\",\n            detailedInfo: \"Maklumat Terperinci\"\n        },\n        // 时间相关\n        time: {\n            days: \"Hari\",\n            day: \"hari\",\n            hours: \"Jam\",\n            minutes: \"Min\",\n            seconds: \"Saat\",\n            expired: \"Tamat\",\n            expiringSoon: \"Penilaian akan tamat tidak lama lagi, sila proses dengan segera\",\n            endsIn: \"Berakhir dalam\",\n            hoursLeft: \"jam lagi\",\n            dayLeft: \"hari lagi\",\n            daysLeft: \"hari lagi\",\n            ended: \"Tamat\"\n        },\n        // 估值页面\n        valuation: {\n            title: \"Halaman Penilaian\",\n            buyPrice: \"Harga Beli\",\n            sellPrice: \"Harga Jual\",\n            remainingTime: \"Masa Berbaki\",\n            duration: \"Tempoh Jualan\",\n            expectedPrice: \"Harga Jualan Dijangka\",\n            expectedProfit: \"Keuntungan Dijangka\",\n            customPrice: \"Harga Beli Tersuai\",\n            fixedPrice: \"Harga Tetap\",\n            fixedPriceSection: \"Harga Tetap\",\n            fixedPriceSetting: \"Tetapan Harga Tetap\",\n            estimatedSellPrice: \"Harga Jual Anggaran\",\n            estimatedSellPriceDesc: \"Harga jual anggaran berdasarkan harga tetap dan pekali jualan\",\n            estimatedProfit: \"Keuntungan Anggaran\",\n            expectedReturn: \"Pulangan Dijangka\",\n            higherReturn: \"Pulangan Lebih Tinggi\",\n            priceOptions: \"Pilihan Harga\",\n            oneDayOption: \"Jualan 1 Hari (Pulangan Stabil)\",\n            twoDayOption: \"Jualan 2 Hari (Potensi Lebih Tinggi)\",\n            startValuation: \"Mula Penilaian\",\n            valuationExpired: \"Penilaian Tamat Tempoh\",\n            valuationExpiring: \"Penilaian akan tamat tidak lama lagi, sila proses dengan segera\",\n            includesFees: \"Termasuk Yuran\",\n            usageGuide: \"Panduan Penggunaan\",\n            buyPriceGuide: \"Tetapan Harga Beli\",\n            buyPriceDesc: \"Tetapkan harga maksimum yang anda sanggup bayar untuk syiling ini. Sistem akan memadankan anda dengan harga terbaik berdasarkan keadaan pasaran.\",\n            sellPriceGuide: \"Anggaran Harga Jualan\",\n            sellPriceDesc: \"Harga jualan anggaran berdasarkan trend pasaran untuk tempoh masa yang dipilih untuk membantu anda membuat keputusan pelaburan terbaik.\",\n            // API错误信息\n            cardNotFound: \"Kad tidak dijumpai\",\n            getValuationFailed: \"Gagal mendapatkan maklumat penilaian\",\n            missingParameters: \"Parameter yang diperlukan hilang\",\n            submitValuationFailed: \"Gagal menghantar penilaian\",\n            submitValuationSuccess: \"Penilaian berjaya dihantar\"\n        },\n        // WhatsApp客服\n        whatsapp: {\n            contactUs: \"Hubungi Kami melalui WhatsApp\",\n            tooltip: \"Perlukan bantuan? Sembang dengan kami di WhatsApp!\",\n            defaultMessage: \"Hello! Saya ingin bertanya tentang syiling dan wang kertas anda.\"\n        }\n    }\n};\n// 当前语言设置\nlet currentLanguage = \"en\"; // 默认英文\n// 获取翻译文本\nfunction t(key, lang) {\n    // 如果没有指定语言，使用当前语言\n    if (!lang) {\n        lang = getCurrentLanguage();\n    }\n    const keys = key.split(\".\");\n    let value = translations[lang];\n    for (const k of keys){\n        if (value && typeof value === \"object\") {\n            value = value[k];\n        } else {\n            return key; // 如果找不到翻译，返回原key\n        }\n    }\n    return value || key;\n}\n// 格式化时间显示\nfunction formatTimeLeft(timeString, lang) {\n    if (!timeString) return \"\";\n    // 如果没有指定语言，使用当前语言\n    if (!lang) {\n        lang = getCurrentLanguage();\n    }\n    // 解析时间字符串，支持多种格式\n    const match = timeString.match(/(\\d+)(小时|天|hour|day|jam|hari)/i);\n    if (!match) return timeString; // 如果无法解析，返回原字符串\n    const number = parseInt(match[1]);\n    const unit = match[2].toLowerCase();\n    // 根据语言和单位返回格式化的时间\n    if (lang === \"zh\") {\n        if (unit.includes(\"小时\") || unit.includes(\"hour\") || unit.includes(\"jam\")) {\n            return `${number}${t(\"time.hoursLeft\", lang)}`;\n        } else if (unit.includes(\"天\") || unit.includes(\"day\") || unit.includes(\"hari\")) {\n            return number === 1 ? `${number}${t(\"time.dayLeft\", lang)}` : `${number}${t(\"time.daysLeft\", lang)}`;\n        }\n    } else if (lang === \"en\") {\n        if (unit.includes(\"小时\") || unit.includes(\"hour\") || unit.includes(\"jam\")) {\n            return `${number} ${t(\"time.hoursLeft\", lang)}`;\n        } else if (unit.includes(\"天\") || unit.includes(\"day\") || unit.includes(\"hari\")) {\n            return number === 1 ? `${number} ${t(\"time.dayLeft\", lang)}` : `${number} ${t(\"time.daysLeft\", lang)}`;\n        }\n    } else if (lang === \"ms\") {\n        if (unit.includes(\"小时\") || unit.includes(\"hour\") || unit.includes(\"jam\")) {\n            return `${t(\"time.endsIn\", lang)} ${number} ${t(\"time.hoursLeft\", lang)}`;\n        } else if (unit.includes(\"天\") || unit.includes(\"day\") || unit.includes(\"hari\")) {\n            return number === 1 ? `${t(\"time.endsIn\", lang)} ${number} ${t(\"time.dayLeft\", lang)}` : `${t(\"time.endsIn\", lang)} ${number} ${t(\"time.daysLeft\", lang)}`;\n        }\n    }\n    return timeString; // 如果无法处理，返回原字符串\n}\n// 设置语言\nfunction setLanguage(lang) {\n    if (translations[lang]) {\n        currentLanguage = lang;\n        // 可以在这里添加本地存储\n        if (false) {}\n    }\n}\n// 获取当前语言\nfunction getCurrentLanguage() {\n    if (false) {}\n    return currentLanguage;\n}\n// 获取所有可用语言\nfunction getAvailableLanguages() {\n    return Object.keys(translations);\n}\n// 获取分类名称的翻译\nfunction getCategoryName(categoryName, lang) {\n    var ref;\n    // 如果没有指定语言，使用当前语言\n    if (!lang) {\n        lang = getCurrentLanguage();\n    }\n    // 尝试从翻译中获取分类名称\n    const categoryTranslations = (ref = translations[lang]) === null || ref === void 0 ? void 0 : ref.category;\n    if (categoryTranslations && categoryTranslations[categoryName]) {\n        return categoryTranslations[categoryName];\n    }\n    // 如果没有找到翻译，返回原名称\n    return categoryName;\n}\n// 初始化语言设置\nfunction initializeLanguage() {\n    if (false) {}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/i18n.js\n");

/***/ }),

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nvar _htmlContext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\nexports[\"default\"] = Document;\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache() {\n    if (typeof WeakMap !== \"function\") return null;\n    var cache = new WeakMap();\n    _getRequireWildcardCache = function() {\n        return cache;\n    };\n    return cache;\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache();\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var ref, ref1;\n            return el == null ? void 0 : (ref = el.props) == null ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref1.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var ref;\n    const { assetPrefix , buildManifest , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) == null ? void 0 : ref.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix , scriptLoader , crossOrigin , nextScriptWorkers  } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet  } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var ref, ref2;\n            return hasComponentProps(child) && (child == null ? void 0 : (ref = child.props) == null ? void 0 : (ref2 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref2.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy , src , children: scriptChildren , dangerouslySetInnerHTML , ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, srcProps, scriptProps, {\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            }));\n        }));\n    } catch (err) {\n        if ((0, _isError).default(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        var _defer;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n            key: scriptProps.src || index,\n            defer: (_defer = scriptProps.defer) != null ? _defer : !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin , nonce , ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nclass Head extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts ,  } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , devOnlyCacheBusterQueryString , crossOrigin ,  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , scriptLoader , crossOrigin ,  } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })), \n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader  } = this.context;\n        const { nonce , crossOrigin  } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy , children , dangerouslySetInnerHTML , src , ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            }));\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var ref5, ref3;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (ref5 = c.props) == null ? void 0 : ref5.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>{\n                var ref, ref4;\n                return c == null ? void 0 : (ref = c.props) == null ? void 0 : (ref4 = ref.href) == null ? void 0 : ref4.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (ref3 = c.props) == null ? void 0 : ref3.children) {\n                const newProps1 = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps1);\n            }\n            return c;\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , optimizeCss , optimizeFonts ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    cssPreloads.push(c);\n                } else {\n                    c && otherHeadElements.push(c);\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var ref;\n                const isReactHelmet = child == null ? void 0 : (ref = child.props) == null ? void 0 : ref[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var ref6;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (ref6 = child.props) == null ? void 0 : ref6.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        var _nonce, _nonce1;\n        return /*#__PURE__*/ _react.default.createElement(\"head\", Object.assign({}, getHeadHTMLProps(this.props)), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }),  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce = this.props.nonce) != null ? _nonce : \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce1 = this.props.nonce) != null ? _nonce1 : \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nexports.Head = Head;\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var ref10, ref7, ref8, ref9;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (ref10 = children.find((child)=>child.type === Head)) == null ? void 0 : (ref7 = ref10.props) == null ? void 0 : ref7.children;\n    const bodyChildren = (ref8 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (ref9 = ref8.props) == null ? void 0 : ref9.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ], \n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var ref;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((ref = child.type) == null ? void 0 : ref.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }, \n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__ , largePageDataBytes  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape).htmlEscapeJsonString(data);\n        } catch (err) {\n            if ((0, _isError).default(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles, \n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nexports.NextScript = NextScript;\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale , scriptLoader , __NEXT_DATA__ ,  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", Object.assign({}, props, {\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    }));\n}\nfunction Main() {\n    const { docComponentsRendered  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\n\n\nfunction Document() {\n    const currentLang = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getCurrentLanguage)();\n    // 语言映射\n    const langMap = {\n        \"en\": \"en\",\n        \"zh\": \"zh-CN\",\n        \"ms\": \"ms-MY\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: langMap[currentLang] || \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_document.js\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Pacifico&family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_document.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_document.js\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_document.js\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_document.js\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_document.js\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_document.js\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_document.js\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQuanMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFBNEQ7QUFDWjtBQUVqQyxTQUFTSyxRQUFRLEdBQUc7SUFDakMsTUFBTUMsV0FBVyxHQUFHRiw2REFBa0IsRUFBRTtJQUV4QyxPQUFPO0lBQ1AsTUFBTUcsT0FBTyxHQUFHO1FBQ2QsSUFBSSxFQUFFLElBQUk7UUFDVixJQUFJLEVBQUUsT0FBTztRQUNiLElBQUksRUFBRSxPQUFPO0tBQ2Q7SUFFRCxxQkFDRSw4REFBQ1AsK0NBQUk7UUFBQ1EsSUFBSSxFQUFFRCxPQUFPLENBQUNELFdBQVcsQ0FBQyxJQUFJLElBQUk7OzBCQUN0Qyw4REFBQ0wsK0NBQUk7O2tDQUVILDhEQUFDUSxNQUFJO3dCQUNIQyxJQUFJLEVBQUMsa0VBQWtFO3dCQUN2RUMsR0FBRyxFQUFDLFlBQVk7Ozs7OzRCQUNoQjtrQ0FFRiw4REFBQ0YsTUFBSTt3QkFDSEMsSUFBSSxFQUFDLHNHQUFzRzt3QkFDM0dDLEdBQUcsRUFBQyxZQUFZOzs7Ozs0QkFDaEI7a0NBRUYsOERBQUNDLE1BQUk7d0JBQUNDLE9BQU8sRUFBQyxPQUFPOzs7Ozs0QkFBRzs7Ozs7O29CQUNuQjswQkFDUCw4REFBQ0MsTUFBSTs7a0NBQ0gsOERBQUNaLCtDQUFJOzs7OzRCQUFHO2tDQUNSLDhEQUFDQyxxREFBVTs7Ozs0QkFBRzs7Ozs7O29CQUNUOzs7Ozs7WUFDRixDQUNSO0FBQ0gsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvaW4tdHJhZGluZy1wbGF0Zm9ybS8uL3BhZ2VzL19kb2N1bWVudC5qcz81MzhiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50J1xuaW1wb3J0IHsgZ2V0Q3VycmVudExhbmd1YWdlIH0gZnJvbSAnLi4vbGliL2kxOG4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xuICBjb25zdCBjdXJyZW50TGFuZyA9IGdldEN1cnJlbnRMYW5ndWFnZSgpO1xuXG4gIC8vIOivreiogOaYoOWwhFxuICBjb25zdCBsYW5nTWFwID0ge1xuICAgICdlbic6ICdlbicsXG4gICAgJ3poJzogJ3poLUNOJyxcbiAgICAnbXMnOiAnbXMtTVknXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8SHRtbCBsYW5nPXtsYW5nTWFwW2N1cnJlbnRMYW5nXSB8fCAnZW4nfT5cbiAgICAgIDxIZWFkPlxuICAgICAgICB7LyogUmVtaXggSWNvbiBDU1MgKi99XG4gICAgICAgIDxsaW5rXG4gICAgICAgICAgaHJlZj1cImh0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0vcmVtaXhpY29uQDMuNS4wL2ZvbnRzL3JlbWl4aWNvbi5jc3NcIlxuICAgICAgICAgIHJlbD1cInN0eWxlc2hlZXRcIlxuICAgICAgICAvPlxuICAgICAgICB7LyogR29vZ2xlIEZvbnRzICovfVxuICAgICAgICA8bGlua1xuICAgICAgICAgIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PVBhY2lmaWNvJmZhbWlseT1JbnRlcjp3Z2h0QDMwMDs0MDA7NTAwOzYwMDs3MDAmZGlzcGxheT1zd2FwXCJcbiAgICAgICAgICByZWw9XCJzdHlsZXNoZWV0XCJcbiAgICAgICAgLz5cbiAgICAgICAgey8qIE1ldGEgdGFncyAqL31cbiAgICAgICAgPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxib2R5PlxuICAgICAgICA8TWFpbiAvPlxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvSHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJnZXRDdXJyZW50TGFuZ3VhZ2UiLCJEb2N1bWVudCIsImN1cnJlbnRMYW5nIiwibGFuZ01hcCIsImxhbmciLCJsaW5rIiwiaHJlZiIsInJlbCIsIm1ldGEiLCJjaGFyU2V0IiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "./node_modules/next/document.js":
/*!***************************************!*\
  !*** ./node_modules/next/document.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/pages/_document */ \"./node_modules/next/dist/pages/_document.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kb2N1bWVudC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxpSEFBa0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2luLXRyYWRpbmctcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvbmV4dC9kb2N1bWVudC5qcz85YTE0Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L3BhZ2VzL19kb2N1bWVudCcpXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/document.js\n");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_document.js"));
module.exports = __webpack_exports__;

})();