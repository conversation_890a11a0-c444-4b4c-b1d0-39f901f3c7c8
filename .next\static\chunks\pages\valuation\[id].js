/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/valuation/[id]"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cvaluation%5C%5Bid%5D.js&page=%2Fvaluation%2F%5Bid%5D!":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cvaluation%5C%5Bid%5D.js&page=%2Fvaluation%2F%5Bid%5D! ***!
  \*************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/valuation/[id]\",\n      function () {\n        return __webpack_require__(/*! ./pages/valuation/[id].js */ \"./pages/valuation/[id].js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/valuation/[id]\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDcHktaWRlJTVDd3VkYWxhbmctc2hvcCU1Q3BhZ2VzJTVDdmFsdWF0aW9uJTVDJTVCaWQlNUQuanMmcGFnZT0lMkZ2YWx1YXRpb24lMkYlNUJpZCU1RCEuanMiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw0REFBMkI7QUFDbEQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzgwN2QiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi92YWx1YXRpb24vW2lkXVwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvdmFsdWF0aW9uL1tpZF0uanNcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL3ZhbHVhdGlvbi9baWRdXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cvaluation%5C%5Bid%5D.js&page=%2Fvaluation%2F%5Bid%5D!\n"));

/***/ }),

/***/ "./components/CountdownTimer.js":
/*!**************************************!*\
  !*** ./components/CountdownTimer.js ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CountdownTimer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction CountdownTimer(param) {\n    var _duration = param.duration, duration = _duration === void 0 ? 24 : _duration, onComplete = param.onComplete;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(duration * 60 * 60), timeLeft = ref[0], setTimeLeft = ref[1]; // 转换为秒\n    var intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        // 清除之前的定时器\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        // 如果时间已经结束\n        if (timeLeft <= 0) {\n            if (onComplete) {\n                onComplete();\n            }\n            return;\n        }\n        // 创建新的定时器，每秒更新一次\n        intervalRef.current = setInterval(function() {\n            setTimeLeft(function(prev) {\n                var newTime = prev - 1;\n                if (newTime <= 0) {\n                    if (onComplete) {\n                        onComplete();\n                    }\n                    return 0;\n                }\n                return newTime;\n            });\n        }, 1000);\n        // 清理函数\n        return function() {\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n        };\n    }, [\n        onComplete\n    ]); // 移除 timeLeft 依赖，避免重复创建定时器\n    var formatTime = function(seconds) {\n        var hours = Math.floor(seconds / 3600);\n        var minutes = Math.floor(seconds % 3600 / 60);\n        var secs = seconds % 60;\n        return {\n            hours: hours.toString().padStart(2, \"0\"),\n            minutes: minutes.toString().padStart(2, \"0\"),\n            seconds: secs.toString().padStart(2, \"0\")\n        };\n    };\n    var ref1 = formatTime(timeLeft), hours = ref1.hours, minutes = ref1.minutes, seconds = ref1.seconds;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center space-x-2 text-lg font-mono font-semibold text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 rounded-md px-3 py-2 min-w-[45px] text-center border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: hours\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-red-500 font-normal\",\n                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"time.hours\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-red-400\",\n                        children: \":\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 rounded-md px-3 py-2 min-w-[45px] text-center border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: minutes\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-red-500 font-normal\",\n                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"time.minutes\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-red-400\",\n                        children: \":\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 rounded-md px-3 py-2 min-w-[45px] text-center border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: seconds\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-red-500 font-normal\",\n                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"time.seconds\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            timeLeft <= 3600 && timeLeft > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 text-xs text-orange-600 bg-orange-50 rounded-md p-2 text-center border border-orange-200\",\n                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"time.expiringSoon\")\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this),\n            timeLeft <= 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 text-xs text-red-600 bg-red-50 rounded-md p-2 text-center border border-red-200\",\n                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"time.expired\")\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\CountdownTimer.js\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s(CountdownTimer, \"SFTphFnzFwOhLDljfDH7ipCwULo=\");\n_c = CountdownTimer;\nvar _c;\n$RefreshReg$(_c, \"CountdownTimer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0NvdW50ZG93blRpbWVyLmpzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBOztBQUFvRDtBQUNwQjtBQUVqQixTQUFTSSxjQUFjLENBQUMsS0FBNkIsRUFBRTtvQkFBL0IsS0FBNkIsQ0FBM0JDLFFBQVEsRUFBUkEsUUFBUSwwQkFBRyxFQUFFLGNBQUVDLFVBQVUsR0FBM0IsS0FBNkIsQ0FBWkEsVUFBVTs7SUFDaEUsSUFBZ0NOLEdBQTRCLEdBQTVCQSwrQ0FBUSxDQUFDSyxRQUFRLEdBQUcsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFyREUsUUFBUSxHQUFpQlAsR0FBNEIsR0FBN0MsRUFBRVEsV0FBVyxHQUFJUixHQUE0QixHQUFoQyxFQUFrQyxPQUFPO0lBQ3JFLElBQU1TLFdBQVcsR0FBR1AsNkNBQU0sQ0FBQyxJQUFJLENBQUM7SUFFaENELGdEQUFTLENBQUMsV0FBTTtRQUNkLFdBQVc7UUFDWCxJQUFJUSxXQUFXLENBQUNDLE9BQU8sRUFBRTtZQUN2QkMsYUFBYSxDQUFDRixXQUFXLENBQUNDLE9BQU8sQ0FBQyxDQUFDO1FBQ3JDLENBQUM7UUFFRCxXQUFXO1FBQ1gsSUFBSUgsUUFBUSxJQUFJLENBQUMsRUFBRTtZQUNqQixJQUFJRCxVQUFVLEVBQUU7Z0JBQ2RBLFVBQVUsRUFBRSxDQUFDO1lBQ2YsQ0FBQztZQUNELE9BQU87UUFDVCxDQUFDO1FBRUQsaUJBQWlCO1FBQ2pCRyxXQUFXLENBQUNDLE9BQU8sR0FBR0UsV0FBVyxDQUFDLFdBQU07WUFDdENKLFdBQVcsQ0FBQ0ssU0FBQUEsSUFBSSxFQUFJO2dCQUNsQixJQUFNQyxPQUFPLEdBQUdELElBQUksR0FBRyxDQUFDO2dCQUN4QixJQUFJQyxPQUFPLElBQUksQ0FBQyxFQUFFO29CQUNoQixJQUFJUixVQUFVLEVBQUU7d0JBQ2RBLFVBQVUsRUFBRSxDQUFDO29CQUNmLENBQUM7b0JBQ0QsT0FBTyxDQUFDLENBQUM7Z0JBQ1gsQ0FBQztnQkFDRCxPQUFPUSxPQUFPLENBQUM7WUFDakIsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFFVCxPQUFPO1FBQ1AsT0FBTyxXQUFNO1lBQ1gsSUFBSUwsV0FBVyxDQUFDQyxPQUFPLEVBQUU7Z0JBQ3ZCQyxhQUFhLENBQUNGLFdBQVcsQ0FBQ0MsT0FBTyxDQUFDLENBQUM7WUFDckMsQ0FBQztRQUNILENBQUMsQ0FBQztJQUNKLENBQUMsRUFBRTtRQUFDSixVQUFVO0tBQUMsQ0FBQyxDQUFDLENBQUMsMkJBQTJCO0lBRTdDLElBQU1TLFVBQVUsR0FBRyxTQUFDQyxPQUFPLEVBQUs7UUFDOUIsSUFBTUMsS0FBSyxHQUFHQyxJQUFJLENBQUNDLEtBQUssQ0FBQ0gsT0FBTyxHQUFHLElBQUksQ0FBQztRQUN4QyxJQUFNSSxPQUFPLEdBQUdGLElBQUksQ0FBQ0MsS0FBSyxDQUFDLE9BQVEsR0FBRyxJQUFJLEdBQUksRUFBRSxDQUFDO1FBQ2pELElBQU1FLElBQUksR0FBR0wsT0FBTyxHQUFHLEVBQUU7UUFFekIsT0FBTztZQUNMQyxLQUFLLEVBQUVBLEtBQUssQ0FBQ0ssUUFBUSxFQUFFLENBQUNDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDO1lBQ3hDSCxPQUFPLEVBQUVBLE9BQU8sQ0FBQ0UsUUFBUSxFQUFFLENBQUNDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDO1lBQzVDUCxPQUFPLEVBQUVLLElBQUksQ0FBQ0MsUUFBUSxFQUFFLENBQUNDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDO1NBQzFDLENBQUM7SUFDSixDQUFDO0lBRUQsSUFBb0NSLElBQW9CLEdBQXBCQSxVQUFVLENBQUNSLFFBQVEsQ0FBQyxFQUFoRFUsS0FBSyxHQUF1QkYsSUFBb0IsQ0FBaERFLEtBQUssRUFBRUcsT0FBTyxHQUFjTCxJQUFvQixDQUF6Q0ssT0FBTyxFQUFFSixPQUFPLEdBQUtELElBQW9CLENBQWhDQyxPQUFPO0lBRS9CLHFCQUNFLDhEQUFDUSxLQUFHOzswQkFDRiw4REFBQ0EsS0FBRztnQkFBQ0MsU0FBUyxFQUFDLDRFQUE0RTs7a0NBQ3pGLDhEQUFDRCxLQUFHO3dCQUFDQyxTQUFTLEVBQUMsa0ZBQWtGOzswQ0FDL0YsOERBQUNELEtBQUc7MENBQUVQLEtBQUs7Ozs7O29DQUFPOzBDQUNsQiw4REFBQ08sS0FBRztnQ0FBQ0MsU0FBUyxFQUFDLGtDQUFrQzswQ0FBRXRCLDRDQUFDLENBQUMsWUFBWSxDQUFDOzs7OztvQ0FBTzs7Ozs7OzRCQUNyRTtrQ0FDTiw4REFBQ3FCLEtBQUc7d0JBQUNDLFNBQVMsRUFBQyxnQ0FBZ0M7a0NBQUMsR0FBQzs7Ozs7NEJBQU07a0NBQ3ZELDhEQUFDRCxLQUFHO3dCQUFDQyxTQUFTLEVBQUMsa0ZBQWtGOzswQ0FDL0YsOERBQUNELEtBQUc7MENBQUVKLE9BQU87Ozs7O29DQUFPOzBDQUNwQiw4REFBQ0ksS0FBRztnQ0FBQ0MsU0FBUyxFQUFDLGtDQUFrQzswQ0FBRXRCLDRDQUFDLENBQUMsY0FBYyxDQUFDOzs7OztvQ0FBTzs7Ozs7OzRCQUN2RTtrQ0FDTiw4REFBQ3FCLEtBQUc7d0JBQUNDLFNBQVMsRUFBQyxnQ0FBZ0M7a0NBQUMsR0FBQzs7Ozs7NEJBQU07a0NBQ3ZELDhEQUFDRCxLQUFHO3dCQUFDQyxTQUFTLEVBQUMsa0ZBQWtGOzswQ0FDL0YsOERBQUNELEtBQUc7MENBQUVSLE9BQU87Ozs7O29DQUFPOzBDQUNwQiw4REFBQ1EsS0FBRztnQ0FBQ0MsU0FBUyxFQUFDLGtDQUFrQzswQ0FBRXRCLDRDQUFDLENBQUMsY0FBYyxDQUFDOzs7OztvQ0FBTzs7Ozs7OzRCQUN2RTs7Ozs7O29CQUNGO1lBRUxJLFFBQVEsSUFBSSxJQUFJLElBQUlBLFFBQVEsR0FBRyxDQUFDLGtCQUMvQiw4REFBQ2lCLEtBQUc7Z0JBQUNDLFNBQVMsRUFBQywrRkFBK0Y7MEJBQzNHdEIsNENBQUMsQ0FBQyxtQkFBbUIsQ0FBQzs7Ozs7b0JBQ25CO1lBR1BJLFFBQVEsSUFBSSxDQUFDLGtCQUNaLDhEQUFDaUIsS0FBRztnQkFBQ0MsU0FBUyxFQUFDLHNGQUFzRjswQkFDbEd0Qiw0Q0FBQyxDQUFDLGNBQWMsQ0FBQzs7Ozs7b0JBQ2Q7Ozs7OztZQUVKLENBQ047QUFDSixDQUFDO0dBdEZ1QkMsY0FBYztBQUFkQSxLQUFBQSxjQUFjIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvQ291bnRkb3duVGltZXIuanM/NGQ2NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB0IH0gZnJvbSAnLi4vbGliL2kxOG4nO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDb3VudGRvd25UaW1lcih7IGR1cmF0aW9uID0gMjQsIG9uQ29tcGxldGUgfSkge1xuICBjb25zdCBbdGltZUxlZnQsIHNldFRpbWVMZWZ0XSA9IHVzZVN0YXRlKGR1cmF0aW9uICogNjAgKiA2MCk7IC8vIOi9rOaNouS4uuenklxuICBjb25zdCBpbnRlcnZhbFJlZiA9IHVzZVJlZihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIOa4hemZpOS5i+WJjeeahOWumuaXtuWZqFxuICAgIGlmIChpbnRlcnZhbFJlZi5jdXJyZW50KSB7XG4gICAgICBjbGVhckludGVydmFsKGludGVydmFsUmVmLmN1cnJlbnQpO1xuICAgIH1cblxuICAgIC8vIOWmguaenOaXtumXtOW3sue7j+e7k+adn1xuICAgIGlmICh0aW1lTGVmdCA8PSAwKSB7XG4gICAgICBpZiAob25Db21wbGV0ZSkge1xuICAgICAgICBvbkNvbXBsZXRlKCk7XG4gICAgICB9XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8g5Yib5bu65paw55qE5a6a5pe25Zmo77yM5q+P56eS5pu05paw5LiA5qyhXG4gICAgaW50ZXJ2YWxSZWYuY3VycmVudCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIHNldFRpbWVMZWZ0KHByZXYgPT4ge1xuICAgICAgICBjb25zdCBuZXdUaW1lID0gcHJldiAtIDE7XG4gICAgICAgIGlmIChuZXdUaW1lIDw9IDApIHtcbiAgICAgICAgICBpZiAob25Db21wbGV0ZSkge1xuICAgICAgICAgICAgb25Db21wbGV0ZSgpO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4gMDtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3VGltZTtcbiAgICAgIH0pO1xuICAgIH0sIDEwMDApO1xuXG4gICAgLy8g5riF55CG5Ye95pWwXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChpbnRlcnZhbFJlZi5jdXJyZW50KSB7XG4gICAgICAgIGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWxSZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW29uQ29tcGxldGVdKTsgLy8g56e76ZmkIHRpbWVMZWZ0IOS+nei1lu+8jOmBv+WFjemHjeWkjeWIm+W7uuWumuaXtuWZqFxuXG4gIGNvbnN0IGZvcm1hdFRpbWUgPSAoc2Vjb25kcykgPT4ge1xuICAgIGNvbnN0IGhvdXJzID0gTWF0aC5mbG9vcihzZWNvbmRzIC8gMzYwMCk7XG4gICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IoKHNlY29uZHMgJSAzNjAwKSAvIDYwKTtcbiAgICBjb25zdCBzZWNzID0gc2Vjb25kcyAlIDYwO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIGhvdXJzOiBob3Vycy50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyksXG4gICAgICBtaW51dGVzOiBtaW51dGVzLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKSxcbiAgICAgIHNlY29uZHM6IHNlY3MudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpXG4gICAgfTtcbiAgfTtcblxuICBjb25zdCB7IGhvdXJzLCBtaW51dGVzLCBzZWNvbmRzIH0gPSBmb3JtYXRUaW1lKHRpbWVMZWZ0KTtcblxuICByZXR1cm4gKFxuICAgIDxkaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yIHRleHQtbGcgZm9udC1tb25vIGZvbnQtc2VtaWJvbGQgdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS0xMDAgcm91bmRlZC1tZCBweC0zIHB5LTIgbWluLXctWzQ1cHhdIHRleHQtY2VudGVyIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICA8ZGl2Pntob3Vyc308L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtNTAwIGZvbnQtbm9ybWFsXCI+e3QoJ3RpbWUuaG91cnMnKX08L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1yZWQtNDAwXCI+OjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktMTAwIHJvdW5kZWQtbWQgcHgtMyBweS0yIG1pbi13LVs0NXB4XSB0ZXh0LWNlbnRlciBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPGRpdj57bWludXRlc308L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtNTAwIGZvbnQtbm9ybWFsXCI+e3QoJ3RpbWUubWludXRlcycpfTwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXJlZC00MDBcIj46PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS0xMDAgcm91bmRlZC1tZCBweC0zIHB5LTIgbWluLXctWzQ1cHhdIHRleHQtY2VudGVyIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICA8ZGl2PntzZWNvbmRzfTwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXJlZC01MDAgZm9udC1ub3JtYWxcIj57dCgndGltZS5zZWNvbmRzJyl9PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHt0aW1lTGVmdCA8PSAzNjAwICYmIHRpbWVMZWZ0ID4gMCAmJiAoIC8vIOacgOWQjuS4gOWwj+aXtuaYvuekuuitpuWRilxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgdGV4dC14cyB0ZXh0LW9yYW5nZS02MDAgYmctb3JhbmdlLTUwIHJvdW5kZWQtbWQgcC0yIHRleHQtY2VudGVyIGJvcmRlciBib3JkZXItb3JhbmdlLTIwMFwiPlxuICAgICAgICAgIHt0KCd0aW1lLmV4cGlyaW5nU29vbicpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHt0aW1lTGVmdCA8PSAwICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHRleHQteHMgdGV4dC1yZWQtNjAwIGJnLXJlZC01MCByb3VuZGVkLW1kIHAtMiB0ZXh0LWNlbnRlciBib3JkZXIgYm9yZGVyLXJlZC0yMDBcIj5cbiAgICAgICAgICB7dCgndGltZS5leHBpcmVkJyl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsInQiLCJDb3VudGRvd25UaW1lciIsImR1cmF0aW9uIiwib25Db21wbGV0ZSIsInRpbWVMZWZ0Iiwic2V0VGltZUxlZnQiLCJpbnRlcnZhbFJlZiIsImN1cnJlbnQiLCJjbGVhckludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJwcmV2IiwibmV3VGltZSIsImZvcm1hdFRpbWUiLCJzZWNvbmRzIiwiaG91cnMiLCJNYXRoIiwiZmxvb3IiLCJtaW51dGVzIiwic2VjcyIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/CountdownTimer.js\n"));

/***/ }),

/***/ "./components/Header.js":
/*!******************************!*\
  !*** ./components/Header.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageSwitcher */ \"./components/LanguageSwitcher.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction Header(param) {\n    var siteConfig = param.siteConfig, currentPage = param.currentPage;\n    var _this = this;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), searchQuery = ref[0], setSearchQuery = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isMenuOpen = ref1[0], setIsMenuOpen = ref1[1];\n    var menuRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function handleClickOutside(event) {\n            if (menuRef.current && !menuRef.current.contains(event.target)) {\n                setIsMenuOpen(false);\n            }\n        };\n        if (isMenuOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isMenuOpen\n    ]);\n    // 使用默认值以防siteConfig未传入\n    var config = siteConfig || {\n        title: \"CoinMarket\",\n        logo: \"CoinMarket\",\n        navigation: []\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-4 sm:px-6 py-3 sm:py-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        ref: menuRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: function() {\n                                    return setIsMenuOpen(!isMenuOpen);\n                                },\n                                className: \"flex items-center justify-center w-10 h-10 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                \"aria-label\": (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"header.menu\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-0.5 bg-gray-600 rounded-full transition-transform duration-200 \".concat(isMenuOpen ? \"rotate-45 translate-y-1.5\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-0.5 bg-gray-600 rounded-full transition-opacity duration-200 \".concat(isMenuOpen ? \"opacity-0\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-0.5 bg-gray-600 rounded-full transition-transform duration-200 \".concat(isMenuOpen ? \"-rotate-45 -translate-y-1.5\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in\",\n                                children: currentPage === \"about\" ? // 在公司简介页面显示首页链接\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200\",\n                                        onClick: function() {\n                                            return setIsMenuOpen(false);\n                                        },\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.home\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 59,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 58,\n                                    columnNumber: 19\n                                }, this) : // 在其他页面显示公司简介链接\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/about\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200\",\n                                        onClick: function() {\n                                            return setIsMenuOpen(false);\n                                        },\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.about\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 68,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                className: \"flex items-center hover:opacity-80 transition-opacity duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo.png\",\n                                    alt: config.title || \"CoinMarket\",\n                                    className: \"h-8 sm:h-10 w-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: config.navigation && config.navigation.length > 0 ? config.navigation.map(function(item) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 98,\n                                    columnNumber: 19\n                                }, _this)\n                            }, item.id, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 97,\n                                columnNumber: 17\n                            }, _this);\n                        }) : // 默认导航菜单（作为后备）\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/cards\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.allCoin\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/auction\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.auction\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 112,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/premium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.premium\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 117,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/buy-now\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.buyNow\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 122,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/sports-cards\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"nav.sportsCards\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 126,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/trading-cards\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: \"Trading Cards\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/test-valuation\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-purple-600 hover:text-purple-700 transition-colors whitespace-nowrap font-medium\",\n                                        children: \"Valuation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 137,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/more\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap\",\n                                        children: \"More\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 142,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative hidden sm:block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_4__.t)(\"header.searchPlaceholder\"),\n                                        value: searchQuery,\n                                        onChange: function(e) {\n                                            return setSearchQuery(e.target.value);\n                                        },\n                                        className: \"w-48 lg:w-80 px-4 py-2 pl-10 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-search-line text-gray-400 text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"sm:hidden w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-search-line text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\Header.js\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"IlI1Vc44qbqljMr4gj4jj4iJBBo=\");\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.js\n"));

/***/ }),

/***/ "./components/ImageGallery.js":
/*!************************************!*\
  !*** ./components/ImageGallery.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImageGallery; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ImageModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ImageModal */ \"./components/ImageModal.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction ImageGallery(param) {\n    var images = param.images;\n    var _this = this;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0), currentIndex = ref[0], setCurrentIndex = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), isLoading = ref1[0], setIsLoading = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), isModalOpen = ref2[0], setIsModalOpen = ref2[1];\n    // 处理图片数据，确保是数组格式\n    var processedImages = function() {\n        if (Array.isArray(images)) {\n            return images.filter(function(img) {\n                return img && img.trim() !== \"\";\n            });\n        } else if (typeof images === \"string\" && images.trim() !== \"\") {\n            try {\n                var parsedImages = JSON.parse(images);\n                return Array.isArray(parsedImages) ? parsedImages.filter(function(img) {\n                    return img && img.trim() !== \"\";\n                }) : [\n                    images\n                ];\n            } catch (e) {\n                return [\n                    images\n                ];\n            }\n        }\n        return [];\n    }();\n    // 自动切换图片\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (processedImages.length <= 1) return;\n        var interval = setInterval(function() {\n            setCurrentIndex(function(prevIndex) {\n                return prevIndex < processedImages.length - 1 ? prevIndex + 1 : 0;\n            });\n        }, 3000); // 每3秒切换一次\n        return function() {\n            return clearInterval(interval);\n        };\n    }, [\n        processedImages.length\n    ]);\n    var handleImageLoad = function() {\n        setIsLoading(false);\n    };\n    var handleImageError = function() {\n        setIsLoading(false);\n    };\n    var openModal = function() {\n        setIsModalOpen(true);\n    };\n    var closeModal = function() {\n        setIsModalOpen(false);\n    };\n    var handleModalIndexChange = function(newIndex) {\n        setCurrentIndex(newIndex);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden shadow-inner cursor-pointer group\",\n                onClick: openModal,\n                children: [\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: processedImages[currentIndex],\n                        alt: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"coin.images\"),\n                        className: \"w-full h-full object-cover object-center transition-all duration-500 group-hover:scale-110 \".concat(isLoading ? \"opacity-0\" : \"opacity-100\"),\n                        onLoad: handleImageLoad,\n                        onError: handleImageError\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            processedImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-3 justify-center\",\n                children: processedImages.map(function(image, index) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return setCurrentIndex(index);\n                        },\n                        className: \"relative w-20 h-24 rounded-lg overflow-hidden transition-all duration-300 border-2 \".concat(index === currentIndex ? \"border-blue-500 ring-2 ring-blue-200 scale-105\" : \"border-gray-200 hover:border-gray-300 hover:scale-105\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: image,\n                                alt: \"卡牌图片 \".concat(index + 1),\n                                className: \"w-full h-full object-cover object-center\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 transition-opacity duration-300 \".concat(index === currentIndex ? \"bg-blue-500/20\" : \"bg-black/20 hover:bg-black/10\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, _this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-gray-50 to-white rounded-xl p-4 border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-image-line\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"coin.images\"),\n                                        \" \",\n                                        currentIndex + 1,\n                                        \" / \",\n                                        processedImages.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                processedImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1 ml-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: \"Auto\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-xs text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-cursor-line\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"coin.clickToView\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            processedImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2 overflow-x-auto pb-2\",\n                    children: processedImages.map(function(image, index) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return setCurrentIndex(index);\n                            },\n                            className: \"flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 \".concat(index === currentIndex ? \"border-blue-500 ring-2 ring-blue-200\" : \"border-gray-200 hover:border-gray-300\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: image,\n                                alt: \"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_3__.t)(\"coin.images\"), \" \").concat(index + 1),\n                                className: \"w-full h-full object-cover\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                                lineNumber: 140,\n                                columnNumber: 17\n                            }, _this)\n                        }, index, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                            lineNumber: 131,\n                            columnNumber: 15\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageModal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: closeModal,\n                images: processedImages,\n                currentIndex: currentIndex,\n                onIndexChange: handleModalIndexChange\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageGallery.js\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageGallery, \"+VxG79Scjktu/ozyJY7NhoxEonI=\");\n_c = ImageGallery;\nvar _c;\n$RefreshReg$(_c, \"ImageGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ImageGallery.js\n"));

/***/ }),

/***/ "./components/ImageModal.js":
/*!**********************************!*\
  !*** ./components/ImageModal.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImageModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction ImageModal(param) {\n    var isOpen = param.isOpen, onClose = param.onClose, images = param.images, currentIndex = param.currentIndex, onIndexChange = param.onIndexChange;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentIndex || 0), currentImageIndex = ref[0], setCurrentImageIndex = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), isLoading = ref1[0], setIsLoading = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1), scale = ref2[0], setScale = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    }), position = ref3[0], setPosition = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), isDragging = ref4[0], setIsDragging = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    }), dragStart = ref5[0], setDragStart = ref5[1];\n    // 同步外部传入的索引\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (currentIndex !== undefined) {\n            setCurrentImageIndex(currentIndex);\n        }\n    }, [\n        currentIndex\n    ]);\n    // 重置缩放和位置\n    var resetTransform = function() {\n        setScale(1);\n        setPosition({\n            x: 0,\n            y: 0\n        });\n    };\n    // 切换图片时重置变换\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        resetTransform();\n        setIsLoading(true);\n    }, [\n        currentImageIndex\n    ]);\n    // 键盘事件处理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (!isOpen) return;\n        var handleKeyDown = function(e) {\n            switch(e.key){\n                case \"Escape\":\n                    onClose();\n                    break;\n                case \"ArrowLeft\":\n                    if (images.length > 1) {\n                        var newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : images.length - 1;\n                        setCurrentImageIndex(newIndex);\n                        onIndexChange === null || onIndexChange === void 0 ? void 0 : onIndexChange(newIndex);\n                    }\n                    break;\n                case \"ArrowRight\":\n                    if (images.length > 1) {\n                        var newIndex1 = currentImageIndex < images.length - 1 ? currentImageIndex + 1 : 0;\n                        setCurrentImageIndex(newIndex1);\n                        onIndexChange === null || onIndexChange === void 0 ? void 0 : onIndexChange(newIndex1);\n                    }\n                    break;\n                case \"+\":\n                case \"=\":\n                    setScale(function(prev) {\n                        return Math.min(prev * 1.2, 5);\n                    });\n                    break;\n                case \"-\":\n                    setScale(function(prev) {\n                        return Math.max(prev / 1.2, 0.5);\n                    });\n                    break;\n                case \"0\":\n                    resetTransform();\n                    break;\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return function() {\n            return document.removeEventListener(\"keydown\", handleKeyDown);\n        };\n    }, [\n        isOpen,\n        currentImageIndex,\n        images.length,\n        onClose,\n        onIndexChange\n    ]);\n    // 鼠标拖拽处理\n    var handleMouseDown = function(e) {\n        if (scale > 1) {\n            setIsDragging(true);\n            setDragStart({\n                x: e.clientX - position.x,\n                y: e.clientY - position.y\n            });\n        }\n    };\n    var handleMouseMove = function(e) {\n        if (isDragging && scale > 1) {\n            setPosition({\n                x: e.clientX - dragStart.x,\n                y: e.clientY - dragStart.y\n            });\n        }\n    };\n    var handleMouseUp = function() {\n        setIsDragging(false);\n    };\n    // 滚轮缩放\n    var handleWheel = function(e) {\n        e.preventDefault();\n        var delta = e.deltaY > 0 ? 0.9 : 1.1;\n        setScale(function(prev) {\n            return Math.min(Math.max(prev * delta, 0.5), 5);\n        });\n    };\n    var goToPrevious = function() {\n        if (images.length > 1) {\n            var newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : images.length - 1;\n            setCurrentImageIndex(newIndex);\n            onIndexChange === null || onIndexChange === void 0 ? void 0 : onIndexChange(newIndex);\n        }\n    };\n    var goToNext = function() {\n        if (images.length > 1) {\n            var newIndex = currentImageIndex < images.length - 1 ? currentImageIndex + 1 : 0;\n            setCurrentImageIndex(newIndex);\n            onIndexChange === null || onIndexChange === void 0 ? void 0 : onIndexChange(newIndex);\n        }\n    };\n    if (!isOpen || !images || images.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 cursor-pointer\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-full max-h-full flex items-center justify-center\",\n                onMouseDown: handleMouseDown,\n                onMouseMove: handleMouseMove,\n                onMouseUp: handleMouseUp,\n                onMouseLeave: handleMouseUp,\n                onWheel: handleWheel,\n                children: [\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-4 border-white border-t-transparent\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: images[currentImageIndex],\n                        alt: \"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"coin.images\"), \" \").concat(currentImageIndex + 1),\n                        className: \"max-w-[90vw] max-h-[90vh] object-contain transition-opacity duration-300 \".concat(isDragging ? \"cursor-grabbing\" : scale > 1 ? \"cursor-grab\" : \"cursor-zoom-in\", \" \").concat(isLoading ? \"opacity-0\" : \"opacity-100\"),\n                        style: {\n                            transform: \"scale(\".concat(scale, \") translate(\").concat(position.x / scale, \"px, \").concat(position.y / scale, \"px)\"),\n                            transformOrigin: \"center center\"\n                        },\n                        onLoad: function() {\n                            return setIsLoading(false);\n                        },\n                        onError: function() {\n                            return setIsLoading(false);\n                        },\n                        onClick: function(e) {\n                            e.stopPropagation();\n                            if (scale === 1) {\n                                setScale(2);\n                            } else {\n                                resetTransform();\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return setScale(function(prev) {\n                                return Math.min(prev * 1.2, 5);\n                            });\n                        },\n                        className: \"bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-2 rounded-full transition-all duration-200\",\n                        title: \"放大 (+)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-zoom-in-line text-lg\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return setScale(function(prev) {\n                                return Math.max(prev / 1.2, 0.5);\n                            });\n                        },\n                        className: \"bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-2 rounded-full transition-all duration-200\",\n                        title: \"缩小 (-)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-zoom-out-line text-lg\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetTransform,\n                        className: \"bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-2 rounded-full transition-all duration-200\",\n                        title: \"重置 (0)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-refresh-line text-lg\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-2 rounded-full transition-all duration-200\",\n                        title: \"关闭 (Esc)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-close-line text-lg\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: goToPrevious,\n                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-3 rounded-full transition-all duration-200\",\n                        title: \"上一张 (←)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-arrow-left-line text-xl\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: goToNext,\n                        className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-3 rounded-full transition-all duration-200\",\n                        title: \"下一张 (→)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-arrow-right-line text-xl\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: [\n                            currentImageIndex + 1,\n                            \" / \",\n                            images.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    scale !== 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm ml-2\",\n                        children: [\n                            Math.round(scale * 100),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-4 right-4 bg-black bg-opacity-50 text-white text-xs px-3 py-2 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"点击图片: 放大/重置\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"滚轮: 缩放\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"拖拽: 移动 (放大时)\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"方向键: 切换图片\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\ImageModal.js\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageModal, \"ZlXsuvF949qjBM4T2FWP967IYDg=\");\n_c = ImageModal;\nvar _c;\n$RefreshReg$(_c, \"ImageModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ImageModal.js\n"));

/***/ }),

/***/ "./components/LanguageSwitcher.js":
/*!****************************************!*\
  !*** ./components/LanguageSwitcher.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LanguageSwitcher; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\nvar _s = $RefreshSig$();\n\n\nvar languages = [\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDEC\\uD83C\\uDDE7\"\n    },\n    {\n        code: \"zh\",\n        name: \"中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    {\n        code: \"ms\",\n        name: \"Bahasa Malaysia\",\n        flag: \"\\uD83C\\uDDF2\\uD83C\\uDDFE\"\n    }\n];\nfunction LanguageSwitcher() {\n    var _this = this;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\"), currentLang = ref[0], setCurrentLang = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), isOpen = ref1[0], setIsOpen = ref1[1];\n    var dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        // 获取当前语言\n        var lang = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getCurrentLanguage)();\n        setCurrentLang(lang);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var handleClickOutside = // 点击外部关闭下拉菜单\n        function handleClickOutside(event) {\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        };\n        if (isOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isOpen\n    ]);\n    var handleLanguageChange = function(langCode) {\n        (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.setLanguage)(langCode);\n        setCurrentLang(langCode);\n        setIsOpen(false);\n        // 刷新页面以应用新语言\n        window.location.reload();\n    };\n    var currentLanguage = languages.find(function(lang) {\n        return lang.code === currentLang;\n    }) || languages[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: function() {\n                    return setIsOpen(!isOpen);\n                },\n                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                \"aria-label\": \"Language Switcher\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: currentLanguage.flag\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hidden sm:block text-sm font-medium text-gray-700\",\n                        children: currentLanguage.name\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"ri-arrow-down-s-line text-gray-400 transition-transform duration-200 \".concat(isOpen ? \"rotate-180\" : \"\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in\",\n                children: languages.map(function(language) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: function() {\n                            return handleLanguageChange(language.code);\n                        },\n                        className: \"w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 \".concat(currentLang === language.code ? \"bg-blue-50 text-blue-600\" : \"text-gray-700\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg\",\n                                children: language.flag\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: language.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, _this),\n                            currentLang === language.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-check-line ml-auto text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                                lineNumber: 80,\n                                columnNumber: 17\n                            }, _this)\n                        ]\n                    }, language.code, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\LanguageSwitcher.js\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageSwitcher, \"YfS1rnW1CbleKHeOzAsHD3ydWrY=\");\n_c = LanguageSwitcher;\nvar _c;\n$RefreshReg$(_c, \"LanguageSwitcher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/LanguageSwitcher.js\n"));

/***/ }),

/***/ "./hooks/useSiteConfig.js":
/*!********************************!*\
  !*** ./hooks/useSiteConfig.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"getCurrentSiteConfig\": function() { return /* binding */ getCurrentSiteConfig; },\n/* harmony export */   \"refreshSiteConfig\": function() { return /* binding */ refreshSiteConfig; },\n/* harmony export */   \"useSiteConfig\": function() { return /* binding */ useSiteConfig; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\n\nvar _s = $RefreshSig$();\n\n\n// 获取默认配置的函数\nvar getDefaultConfig = function() {\n    return {\n        title: \"myduitlama\",\n        description: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_1__.t)(\"about.subtitle\"),\n        logo: \"myduitlama\",\n        navigation: []\n    };\n};\n// 全局配置缓存\nvar globalConfig = null;\nvar configPromise = null;\nfunction useSiteConfig() {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(globalConfig || getDefaultConfig()), siteConfig = ref[0], setSiteConfig = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!globalConfig), loading = ref1[0], setLoading = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null), error = ref2[0], setError = ref2[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        // 如果已经有全局配置，直接使用\n        if (globalConfig) {\n            setSiteConfig(globalConfig);\n            setLoading(false);\n            return;\n        }\n        // 如果正在加载中，等待现有的Promise\n        if (configPromise) {\n            configPromise.then(function(config) {\n                setSiteConfig(config);\n                setLoading(false);\n            }).catch(function(err) {\n                setError(err);\n                setLoading(false);\n            });\n            return;\n        }\n        // 开始新的加载\n        setLoading(true);\n        configPromise = loadSiteConfig();\n        configPromise.then(function(config) {\n            globalConfig = config;\n            setSiteConfig(config);\n            setLoading(false);\n        }).catch(function(err) {\n            console.error(\"Error loading site config:\", err);\n            setError(err);\n            setLoading(false);\n            // 使用默认配置\n            setSiteConfig(getDefaultConfig());\n        }).finally(function() {\n            configPromise = null;\n        });\n    }, []);\n    return {\n        siteConfig: siteConfig,\n        loading: loading,\n        error: error\n    };\n}\n_s(useSiteConfig, \"iRrxyPqodM5BJIsTc13ZKlRI61w=\");\nfunction loadSiteConfig() {\n    return _loadSiteConfig.apply(this, arguments);\n}\nfunction _loadSiteConfig() {\n    _loadSiteConfig = // 加载网站配置的函数\n    (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var response, config, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    _state.trys.push([\n                        0,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        fetch(\"/api/site-config\")\n                    ];\n                case 1:\n                    response = _state.sent();\n                    if (!response.ok) {\n                        throw new Error(\"Failed to fetch site config\");\n                    }\n                    return [\n                        4,\n                        response.json()\n                    ];\n                case 2:\n                    config = _state.sent();\n                    return [\n                        2,\n                        config\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"Error fetching site config:\", error);\n                    return [\n                        2,\n                        getDefaultConfig()\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    });\n    return _loadSiteConfig.apply(this, arguments);\n}\n// 刷新配置的函数（用于后台更新后刷新）\nfunction refreshSiteConfig() {\n    globalConfig = null;\n    return loadSiteConfig().then(function(config) {\n        globalConfig = config;\n        return config;\n    });\n}\n// 获取当前配置的函数（同步）\nfunction getCurrentSiteConfig() {\n    return globalConfig || getDefaultConfig();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useSiteConfig.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getDomainLocale = getDomainLocale;\nvar basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) { var finalLocale, proto, domain, target, detectDomainLocale, normalizeLocalePath; } else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _slicedToArray = (__webpack_require__(/*! @swc/helpers/lib/_sliced_to_array.js */ \"./node_modules/@swc/helpers/lib/_sliced_to_array.js\")[\"default\"]);\nvar _typeOf = (__webpack_require__(/*! @swc/helpers/lib/_type_of.js */ \"./node_modules/@swc/helpers/lib/_type_of.js\")[\"default\"]);\nvar _s = $RefreshSig$();\n\"client\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = void 0;\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _object_without_properties_loose = (__webpack_require__(/*! @swc/helpers/lib/_object_without_properties_loose.js */ \"./node_modules/@swc/helpers/lib/_object_without_properties_loose.js\")[\"default\"]);\nvar _react = _interop_require_default(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nvar _router = __webpack_require__(/*! ../shared/lib/router/router */ \"./node_modules/next/dist/shared/lib/router/router.js\");\nvar _addLocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nvar _routerContext = __webpack_require__(/*! ../shared/lib/router-context */ \"./node_modules/next/dist/shared/lib/router-context.js\");\nvar _appRouterContext = __webpack_require__(/*! ../shared/lib/app-router-context */ \"./node_modules/next/dist/shared/lib/app-router-context.js\");\nvar _useIntersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nvar _getDomainLocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nvar _addBasePath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\n\"client\";\nvar prefetched = {};\nfunction prefetch(router, href, as, options) {\n    if ( false || !router) return;\n    if (!(0, _router).isLocalURL(href)) return;\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    Promise.resolve(router.prefetch(href, as, options)).catch(function(err) {\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n    var curLocale = options && typeof options.locale !== \"undefined\" ? options.locale : router && router.locale;\n    // Join on an invalid URI character\n    prefetched[href + \"%\" + as + (curLocale ? \"%\" + curLocale : \"\")] = true;\n}\nfunction isModifiedEvent(event) {\n    var target = event.currentTarget.target;\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled) {\n    var nodeName = e.currentTarget.nodeName;\n    // anchors inside an svg have a lowercase nodeName\n    var isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || !(0, _router).isLocalURL(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    var navigate = function() {\n        // If the router is an NextRouter instance it will have `beforePopState`\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow: shallow,\n                locale: locale,\n                scroll: scroll\n            });\n        } else {\n            // If `beforePopState` doesn't exist on the router it's the AppRouter.\n            var method = replace ? \"replace\" : \"push\";\n            router[method](href, {\n                forceOptimisticNavigation: !prefetchEnabled\n            });\n        }\n    };\n    if (isAppRouter) {\n        // @ts-expect-error startTransition exists.\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nvar Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    if (true) {\n        var createPropError = function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\".concat(args.key, \"` expects a \").concat(args.expected, \" in `<Link>`, but got `\").concat(args.actual, \"` instead.\") + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        };\n        // TypeScript trick for type-guarding:\n        var requiredPropsGuard = {\n            href: true\n        };\n        var requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach(function(key) {\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : _typeOf(props[key])\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                var _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        var optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        var optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach(function(key) {\n            var valType = _typeOf(props[key]);\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key: key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                var _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        var hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    var children;\n    var hrefProp = props.href, asProp = props.as, childrenProp = props.children, prefetchProp = props.prefetch, passHref = props.passHref, replace = props.replace, shallow = props.shallow, scroll = props.scroll, locale = props.locale, onClick = props.onClick, onMouseEnter = props.onMouseEnter, onTouchStart = props.onTouchStart, _legacyBehavior = props.legacyBehavior, legacyBehavior = _legacyBehavior === void 0 ? Boolean(false) !== true : _legacyBehavior, restProps = _object_without_properties_loose(props, [\n        \"href\",\n        \"as\",\n        \"children\",\n        \"prefetch\",\n        \"passHref\",\n        \"replace\",\n        \"shallow\",\n        \"scroll\",\n        \"locale\",\n        \"onClick\",\n        \"onMouseEnter\",\n        \"onTouchStart\",\n        \"legacyBehavior\"\n    ]);\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ _react.default.createElement(\"a\", null, children);\n    }\n    var p = prefetchProp !== false;\n    var router = _react.default.useContext(_routerContext.RouterContext);\n    // TODO-APP: type error. Remove `as any`\n    var appRouter = _react.default.useContext(_appRouterContext.AppRouterContext);\n    if (appRouter) {\n        router = appRouter;\n    }\n    var ref = _react.default.useMemo(function() {\n        var ref = _slicedToArray((0, _router).resolveHref(router, hrefProp, true), 2), resolvedHref = ref[0], resolvedAs = ref[1];\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _router).resolveHref(router, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        router,\n        hrefProp,\n        asProp\n    ]), href = ref.href, as = ref.as;\n    var previousHref = _react.default.useRef(href);\n    var previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    var child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `'.concat(hrefProp, '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link'));\n            }\n            if (onMouseEnter) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `'.concat(hrefProp, '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link'));\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\".concat(hrefProp, \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"));\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\".concat(hrefProp, \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\") + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    }\n    var childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    var ref1 = _slicedToArray((0, _useIntersection).useIntersection({\n        rootMargin: \"200px\"\n    }), 3), setIntersectionRef = ref1[0], isVisible = ref1[1], resetVisible = ref1[2];\n    var setRef = _react.default.useCallback(function(el) {\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    _react.default.useEffect(function() {\n        var shouldPrefetch = isVisible && p && (0, _router).isLocalURL(href);\n        var curLocale = typeof locale !== \"undefined\" ? locale : router && router.locale;\n        var isPrefetched = prefetched[href + \"%\" + as + (curLocale ? \"%\" + curLocale : \"\")];\n        if (shouldPrefetch && !isPrefetched) {\n            prefetch(router, href, as, {\n                locale: curLocale\n            });\n        }\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        p,\n        router\n    ]);\n    var childProps = {\n        ref: setRef,\n        onClick: function(e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!e.defaultPrevented) {\n                linkClicked(e, router, href, as, replace, shallow, scroll, locale, Boolean(appRouter), p);\n            }\n        },\n        onMouseEnter: function(e) {\n            if (!legacyBehavior && typeof onMouseEnter === \"function\") {\n                onMouseEnter(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            // Check for not prefetch disabled in page using appRouter\n            if (!(!p && appRouter)) {\n                if ((0, _router).isLocalURL(href)) {\n                    prefetch(router, href, as, {\n                        priority: true\n                    });\n                }\n            }\n        },\n        onTouchStart: function(e) {\n            if (!legacyBehavior && typeof onTouchStart === \"function\") {\n                onTouchStart(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            // Check for not prefetch disabled in page using appRouter\n            if (!(!p && appRouter)) {\n                if ((0, _router).isLocalURL(href)) {\n                    prefetch(router, href, as, {\n                        priority: true\n                    });\n                }\n            }\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user\n    if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        var curLocale = typeof locale !== \"undefined\" ? locale : router && router.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        var localeDomain = router && router.isLocaleDomain && (0, _getDomainLocale).getDomainLocale(as, curLocale, router.locales, router.domainLocales);\n        childProps.href = localeDomain || (0, _addBasePath).addBasePath((0, _addLocale).addLocale(as, curLocale, router && router.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ _react.default.createElement(\"a\", Object.assign({}, restProps, childProps), children);\n}, \"xuB00qEWT1T+jc4Svm2qUBtJuIg=\")), \"xuB00qEWT1T+jc4Svm2qUBtJuIg=\");\n_c1 = Link;\nvar _default = Link;\nexports[\"default\"] = _default;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _slicedToArray = (__webpack_require__(/*! @swc/helpers/lib/_sliced_to_array.js */ \"./node_modules/@swc/helpers/lib/_sliced_to_array.js\")[\"default\"]);\nvar _s = $RefreshSig$();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.useIntersection = useIntersection;\nvar _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nvar _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nvar hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nvar observers = new Map();\nvar idList = [];\nfunction createObserver(options) {\n    var id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    var existing = idList.find(function(obj) {\n        return obj.root === id.root && obj.margin === id.margin;\n    });\n    var instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    var elements = new Map();\n    var observer = new IntersectionObserver(function(entries) {\n        entries.forEach(function(entry) {\n            var callback = elements.get(entry.target);\n            var isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id: id,\n        observer: observer,\n        elements: elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    var ref = createObserver(options), id = ref.id, observer = ref.observer, elements = ref.elements;\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            var index = idList.findIndex(function(obj) {\n                return obj.root === id.root && obj.margin === id.margin;\n            });\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    var rootRef = param.rootRef, rootMargin = param.rootMargin, disabled = param.disabled;\n    _s();\n    var isDisabled = disabled || !hasIntersectionObserver;\n    var ref = _slicedToArray((0, _react).useState(false), 2), visible = ref[0], setVisible = ref[1];\n    var ref1 = _slicedToArray((0, _react).useState(null), 2), element = ref1[0], setElement = ref1[1];\n    (0, _react).useEffect(function() {\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            if (element && element.tagName) {\n                var unobserve = observe(element, function(isVisible) {\n                    return isVisible && setVisible(isVisible);\n                }, {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin: rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                var idleCallback = (0, _requestIdleCallback).requestIdleCallback(function() {\n                    return setVisible(true);\n                });\n                return function() {\n                    return (0, _requestIdleCallback).cancelIdleCallback(idleCallback);\n                };\n            }\n        }\n    }, [\n        element,\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible\n    ]);\n    var resetVisible = (0, _react).useCallback(function() {\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\n_s(useIntersection, \"mCSdCffdW7h1A87zcVCmaEd/d2A=\");\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/app-router-context.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-router-context.js ***!
  \*****************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.TemplateContext = exports.GlobalLayoutRouterContext = exports.LayoutRouterContext = exports.AppRouterContext = void 0;\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _react = _interop_require_default(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nvar AppRouterContext = _react.default.createContext(null);\nexports.AppRouterContext = AppRouterContext;\nvar LayoutRouterContext = _react.default.createContext(null);\nexports.LayoutRouterContext = LayoutRouterContext;\nvar GlobalLayoutRouterContext = _react.default.createContext(null);\nexports.GlobalLayoutRouterContext = GlobalLayoutRouterContext;\nvar TemplateContext = _react.default.createContext(null);\nexports.TemplateContext = TemplateContext;\nif (true) {\n    AppRouterContext.displayName = \"AppRouterContext\";\n    LayoutRouterContext.displayName = \"LayoutRouterContext\";\n    GlobalLayoutRouterContext.displayName = \"GlobalLayoutRouterContext\";\n    TemplateContext.displayName = \"TemplateContext\";\n} //# sourceMappingURL=app-router-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/app-router-context.js\n"));

/***/ }),

/***/ "./pages/valuation/[id].js":
/*!*********************************!*\
  !*** ./pages/valuation/[id].js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ValuationPage; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/Header */ \"./components/Header.js\");\n/* harmony import */ var _components_ImageGallery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ImageGallery */ \"./components/ImageGallery.js\");\n/* harmony import */ var _components_CountdownTimer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/CountdownTimer */ \"./components/CountdownTimer.js\");\n/* harmony import */ var _hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../hooks/useSiteConfig */ \"./hooks/useSiteConfig.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../lib/i18n */ \"./lib/i18n.js\");\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ValuationPage() {\n    _s();\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    var id = router.query.id;\n    var ref = (0,_hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__.useSiteConfig)(), siteConfig = ref.siteConfig, configLoading = ref.loading;\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), card = ref1[0], setCard = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), loading = ref2[0], setLoading = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), error = ref3[0], setError = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), buyPrice = ref4[0], setBuyPrice = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), sellPrice = ref5[0], setSellPrice = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1day\"), selectedDuration = ref6[0], setSelectedDuration = ref6[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (id) {\n            loadCard();\n        }\n    }, [\n        id\n    ]);\n    function loadCard() {\n        return _loadCard.apply(this, arguments);\n    }\n    function _loadCard() {\n        _loadCard = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            var response, cardData, fixedPrice, sellMultiplier, error;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            4,\n                            5\n                        ]);\n                        setLoading(true);\n                        return [\n                            4,\n                            fetch(\"/api/cards/\".concat(id))\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            if (response.status === 404) {\n                                setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                            } else {\n                                setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                            }\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        cardData = _state.sent();\n                        setCard(cardData);\n                        fixedPrice = cardData.estimatedValue || cardData.price;\n                        sellMultiplier = cardData.sellMultiplier || 1.050;\n                        setBuyPrice(fixedPrice.toFixed(2));\n                        setSellPrice((fixedPrice * sellMultiplier).toFixed(2));\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error loading card:\", error);\n                        setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return _loadCard.apply(this, arguments);\n    }\n    var handleSubmitValuation = function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            var response, _$error, error;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            5,\n                            ,\n                            6\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/valuation/\".concat(id), {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    buyPrice: buyPrice,\n                                    sellPrice: sellPrice,\n                                    duration: selectedDuration\n                                })\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            2\n                        ];\n                        alert(\"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.success\"), \"!\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPrice\"), \": RM\").concat(buyPrice, \"\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPrice\"), \": RM\").concat(sellPrice, \"\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.duration\"), \": \").concat(selectedDuration === \"1day\" ? (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.oneDayOption\") : (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.twoDayOption\")));\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 3:\n                        _$error = _state.sent();\n                        alert(\"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"), \": \").concat(_$error.error || (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\")));\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            3,\n                            6\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        console.error(\"Error submitting valuation:\", error);\n                        alert((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                        return [\n                            3,\n                            6\n                        ];\n                    case 6:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function handleSubmitValuation() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    if (loading || configLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    siteConfig: siteConfig\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !card) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    siteConfig: siteConfig\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl text-gray-400 mb-4\",\n                                    children: \"\\uD83D\\uDE1E\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                    children: error || (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-blue-600 hover:text-blue-800 transition-colors duration-200\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.back\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            card.name,\n                            \" - \",\n                            (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\"),\n                            \" - \",\n                            siteConfig.title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.title\"), \" - \").concat(card.name)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        siteConfig: siteConfig\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-sm mx-auto px-4 py-4 sm:max-w-2xl sm:px-6 sm:py-6 lg:max-w-7xl lg:px-8 lg:py-8 xl:max-w-[1600px] xl:px-12 2xl:max-w-[1800px] 2xl:px-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center space-x-2 text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6 lg:mb-8 overflow-x-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            className: \"hover:text-purple-600 transition-colors duration-200 whitespace-nowrap\",\n                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.home\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-arrow-right-s-line\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/card/\".concat(card.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            className: \"hover:text-purple-600 transition-colors duration-200 whitespace-nowrap\",\n                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.details\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-arrow-right-s-line\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-purple-600 font-medium whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 sticky top-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-square bg-gray-100 p-4 sm:p-6 lg:p-8\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full rounded-lg overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageGallery__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    images: card.images\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium\",\n                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 sm:p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 173,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.status\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 174,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 172,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-green-600 bg-green-100 px-2 py-1 rounded-md\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.available\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 176,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.remainingTime\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-gray-900 bg-white px-2 py-1 rounded-md border\",\n                                                                            children: [\n                                                                                \"1 \",\n                                                                                (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"time.day\")\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg p-4 text-center border border-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.year\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: card.year\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg p-4 text-center border border-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.grade\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: card.grade\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 196,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-1\",\n                                                            children: card.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: card.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-900 px-6 py-4 text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-lg font-semibold text-gray-200\",\n                                                                        children: \"固定价格\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold mt-1\",\n                                                                        children: [\n                                                                            \"RM\",\n                                                                            parseFloat(buyPrice).toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.includesFees\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-300\",\n                                                                        children: \"(BP)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-6 py-4 bg-gray-50 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-red-600 mb-2 font-medium\",\n                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.remainingTime\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CountdownTimer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            duration: selectedDuration === \"1day\" ? 24 : 48,\n                                                            onComplete: function() {\n                                                                return alert((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.valuationExpired\"));\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-medium text-gray-700 mb-3\",\n                                                                    children: \"固定价格设置\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                            children: \"固定价格\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm\",\n                                                                                    children: \"RM\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 247,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    value: buyPrice,\n                                                                                    readOnly: true,\n                                                                                    className: \"w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-900 bg-gray-50 cursor-not-allowed transition-all duration-200\",\n                                                                                    placeholder: \"1,590.00\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs\",\n                                                                                            children: \"?\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 257,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 256,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 255,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t border-gray-200 my-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: \"预估抛售价格\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600 text-xs\",\n                                                                            children: \"基于固定价格和抛售系数计算的预估抛售价格\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.duration\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 277,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    value: selectedDuration,\n                                                                                    onChange: function(e) {\n                                                                                        return setSelectedDuration(e.target.value);\n                                                                                    },\n                                                                                    className: \"w-full p-2.5 border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-900 focus:border-gray-900 text-sm bg-white transition-all duration-200\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"1day\",\n                                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.oneDayOption\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 285,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"2day\",\n                                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.twoDayOption\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 286,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-green-50 rounded-lg p-4 mb-4 border border-green-200\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-green-700 font-medium mb-1\",\n                                                                                        children: \"预估抛售价格\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 293,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-2xl font-bold text-green-800 mb-1\",\n                                                                                        children: [\n                                                                                            \"RM\",\n                                                                                            parseFloat(sellPrice).toLocaleString()\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 294,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-green-600\",\n                                                                                        children: [\n                                                                                            \"预估利润: +RM\",\n                                                                                            parseFloat(sellPrice - buyPrice).toLocaleString()\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 297,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleSubmitValuation,\n                                                                                className: \"w-full bg-gray-900 hover:bg-gray-800 text-white py-3 px-4 rounded-md font-medium text-sm transition-all duration-200\",\n                                                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.startValuation\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-4xl mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4 text-center\",\n                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.usageGuide\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"\\uD83D\\uDCB0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPriceGuide\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPriceDesc\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"\\uD83D\\uDCC8\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPriceGuide\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPriceDesc\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ValuationPage, \"iaMa5+/wjPnPymBxeZku1OdikGM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__.useSiteConfig\n    ];\n});\n_c = ValuationPage;\nvar _c;\n$RefreshReg$(_c, \"ValuationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/valuation/[id].js\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cpy-ide%5Cwudalang-shop%5Cpages%5Cvaluation%5C%5Bid%5D.js&page=%2Fvaluation%2F%5Bid%5D!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);