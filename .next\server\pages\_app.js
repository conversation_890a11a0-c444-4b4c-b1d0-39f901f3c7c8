/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./components/WhatsAppButton.js":
/*!**************************************!*\
  !*** ./components/WhatsAppButton.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WhatsAppButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n\n\n\nfunction WhatsAppButton({ whatsappConfig  }) {\n    const { 0: isVisible , 1: setIsVisible  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { 0: isHovered , 1: setIsHovered  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 调试信息（可选，生产环境可移除）\n    // console.log('WhatsApp Button Config:', whatsappConfig);\n    // 如果没有配置WhatsApp号码，不显示按钮\n    if (!(whatsappConfig === null || whatsappConfig === void 0 ? void 0 : whatsappConfig.phone) || !(whatsappConfig === null || whatsappConfig === void 0 ? void 0 : whatsappConfig.enabled)) {\n        console.log(\"WhatsApp Button not showing:\", {\n            phone: whatsappConfig === null || whatsappConfig === void 0 ? void 0 : whatsappConfig.phone,\n            enabled: whatsappConfig === null || whatsappConfig === void 0 ? void 0 : whatsappConfig.enabled\n        });\n        return null;\n    }\n    // 页面滚动时显示按钮\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsVisible(window.scrollY > 50);\n        };\n        // 初始显示按钮\n        setIsVisible(true);\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // 格式化WhatsApp链接\n    const formatWhatsAppLink = ()=>{\n        // 清理电话号码，移除所有非数字字符\n        const cleanPhone = whatsappConfig.phone.replace(/\\D/g, \"\");\n        // 确保号码以国家代码开头（如果没有，默认添加60为马来西亚）\n        let formattedPhone = cleanPhone;\n        if (!cleanPhone.startsWith(\"60\") && cleanPhone.length <= 10) {\n            formattedPhone = \"60\" + cleanPhone;\n        }\n        // 默认消息\n        const defaultMessage = whatsappConfig.message || (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"whatsapp.defaultMessage\");\n        const encodedMessage = encodeURIComponent(defaultMessage);\n        return `https://wa.me/${formattedPhone}?text=${encodedMessage}`;\n    };\n    const handleClick = ()=>{\n        console.log(\"WhatsApp button clicked!\");\n        const whatsappLink = formatWhatsAppLink();\n        console.log(\"Generated WhatsApp link:\", whatsappLink);\n        try {\n            // 尝试打开WhatsApp链接\n            const newWindow = window.open(whatsappLink, \"_blank\");\n            // 检查是否成功打开窗口\n            if (newWindow) {\n                console.log(\"WhatsApp link opened successfully\");\n                // 设置一个延时检查，如果窗口很快关闭，说明没有安装WhatsApp\n                setTimeout(()=>{\n                    if (newWindow.closed) {\n                        console.log(\"WhatsApp window was closed, might not be installed\");\n                        alert(\"请确保您的设备已安装WhatsApp，或者访问 https://web.whatsapp.com/\");\n                    }\n                }, 1000);\n            } else {\n                console.log(\"Failed to open WhatsApp window, trying direct navigation\");\n                // 如果弹窗被阻止，直接跳转\n                window.location.href = whatsappLink;\n            }\n        } catch (error) {\n            console.error(\"Error opening WhatsApp link:\", error);\n            alert(\"无法打开WhatsApp。请手动访问: \" + whatsappLink);\n        }\n    };\n    // console.log('WhatsApp button render - isVisible:', isVisible);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed bottom-6 right-6 z-50 transition-all duration-300 transform ${isVisible ? \"translate-y-0 opacity-100\" : \"translate-y-16 opacity-0\"}`,\n        style: {\n            pointerEvents: isVisible ? \"auto\" : \"none\"\n        },\n        onClick: (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            handleClick();\n        },\n        children: [\n            isHovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-16 right-0 mb-2 mr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg border border-gray-200 px-4 py-2 max-w-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-800 font-medium\",\n                            children: whatsappConfig.tooltip || (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"whatsapp.tooltip\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 right-4 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white border-r border-b border-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    console.log(\"Button clicked!\", e);\n                    handleClick();\n                },\n                onMouseEnter: ()=>setIsHovered(true),\n                onMouseLeave: ()=>setIsHovered(false),\n                className: \"bg-green-500 hover:bg-green-600 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 group\",\n                \"aria-label\": (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.t)(\"whatsapp.contactUs\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-8 h-8 group-hover:scale-110 transition-transform duration-200\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.106\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-full bg-green-500 animate-ping opacity-20\"\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\components\\\\WhatsAppButton.js\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/WhatsAppButton.js\n");

/***/ }),

/***/ "./hooks/useAuth.js":
/*!**************************!*\
  !*** ./hooks/useAuth.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"AuthProvider\": () => (/* binding */ AuthProvider),\n/* harmony export */   \"useAuth\": () => (/* binding */ useAuth),\n/* harmony export */   \"withAuth\": () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction AuthProvider({ children  }) {\n    const { 0: user , 1: setUser  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { 0: loading , 1: setLoading  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 检查用户认证状态\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/auth/me\");\n            if (response.ok) {\n                const data = await response.json();\n                console.log(\"Auth check successful:\", data.user);\n                setUser(data.user);\n            } else {\n                console.log(\"Auth check failed:\", response.status);\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Auth check error:\", error);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 登录函数\n    const login = async (username, password)=>{\n        try {\n            const response = await fetch(\"/api/admin/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    username,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                console.log(\"Login successful in context:\", data.user);\n                setUser(data.user);\n                return {\n                    success: true,\n                    user: data.user\n                };\n            } else {\n                console.log(\"Login failed:\", data.message);\n                return {\n                    success: false,\n                    error: data.message\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"网络错误，请重试\"\n            };\n        }\n    };\n    // 登出函数\n    const logout = async ()=>{\n        try {\n            console.log(\"Starting logout process...\");\n            // 调用登出API\n            const response = await fetch(\"/api/admin/auth/logout\", {\n                method: \"POST\",\n                credentials: \"include\" // 确保包含cookies\n            });\n            console.log(\"Logout API response:\", response.status);\n        } catch (error) {\n            console.error(\"Logout API error:\", error);\n        } finally{\n            // 无论API调用是否成功，都清除本地状态\n            console.log(\"Clearing local auth state...\");\n            setUser(null);\n            // 手动清除可能存在的cookie（客户端清除）\n            document.cookie = \"adminToken=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n            // 使用硬重定向确保完全清除状态，添加logout参数\n            console.log(\"Redirecting to login page...\");\n            window.location.href = \"/admin/login?logout=true\";\n        }\n    };\n    // 初始化时检查认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        checkAuth,\n        isAuthenticated: !!user,\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === \"admin\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// 高阶组件：需要认证的页面\nfunction withAuth(WrappedComponent) {\n    return function AuthenticatedComponent(props) {\n        const { user , loading  } = useAuth();\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            console.log(\"withAuth useEffect:\", {\n                user,\n                loading,\n                pathname: router.pathname\n            });\n            if (!loading && !user && router.pathname !== \"/admin/login\") {\n                console.log(\"Redirecting to login page\");\n                router.replace(\"/admin/login\");\n            }\n        }, [\n            user,\n            loading,\n            router\n        ]);\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"验证登录状态...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-white flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"重定向到登录页面...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\hooks\\\\useAuth.js\",\n            lineNumber: 152,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useAuth.js\n");

/***/ }),

/***/ "./lib/i18n.js":
/*!*********************!*\
  !*** ./lib/i18n.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"formatTimeLeft\": () => (/* binding */ formatTimeLeft),\n/* harmony export */   \"getAvailableLanguages\": () => (/* binding */ getAvailableLanguages),\n/* harmony export */   \"getCategoryName\": () => (/* binding */ getCategoryName),\n/* harmony export */   \"getCurrentLanguage\": () => (/* binding */ getCurrentLanguage),\n/* harmony export */   \"initializeLanguage\": () => (/* binding */ initializeLanguage),\n/* harmony export */   \"setLanguage\": () => (/* binding */ setLanguage),\n/* harmony export */   \"t\": () => (/* binding */ t),\n/* harmony export */   \"translations\": () => (/* binding */ translations)\n/* harmony export */ });\n// 国际化配置文件\nconst translations = {\n    en: {\n        // 通用\n        common: {\n            loading: \"Loading...\",\n            error: \"Error\",\n            success: \"Success\",\n            cancel: \"Cancel\",\n            confirm: \"Confirm\",\n            save: \"Save\",\n            delete: \"Delete\",\n            edit: \"Edit\",\n            view: \"View\",\n            back: \"Back\",\n            next: \"Next\",\n            previous: \"Previous\",\n            search: \"Search\",\n            filter: \"Filter\",\n            sort: \"Sort\",\n            more: \"More\"\n        },\n        // 导航\n        nav: {\n            home: \"Home\",\n            allCoin: \"All Coin\",\n            auction: \"Auction\",\n            premium: \"Premium\",\n            buyNow: \"Buy Now\",\n            sportsCards: \"Commemorative Coins\",\n            tradingCards: \"Vintage Banknotes\",\n            valuation: \"Valuation\",\n            admin: \"Admin\",\n            about: \"About Us\"\n        },\n        // Header\n        header: {\n            searchPlaceholder: \"Search coins, banknotes, etc...\",\n            menu: \"Menu\"\n        },\n        // 公司简介页面\n        about: {\n            title: \"About Us\",\n            subtitle: \"myduitlama is a platform focused on collecting, trading and auctioning ancient coins and old banknotes from Malaysia and countries around the world. We are committed to providing authentic, professional and high-quality collectibles and services to every collector and investor.\",\n            aboutUsTitle: \"About Us\",\n            aboutUsContent1: \"We specialize in the acquisition, sale and auction of old banknotes and coins, while providing consulting services for international authoritative grading (such as PMG, PCGS), and can commission the auction of high-value collections on behalf of clients.\",\n            aboutUsContent2: \"We have trading and auction channels covering China, the United States, the United Kingdom, Japan and other major countries, and are committed to sharing collecting knowledge and market conditions to help clients make more informed decisions in collecting and investing.\",\n            professionalPlatform: \"Professional Collection Platform\",\n            trustedExpert: \"Trusted Ancient Coin and Banknote Trading Expert\",\n            contactUs: \"Contact Us\",\n            contactInfo: \"Contact Information\",\n            email: \"Email\",\n            phone: \"Phone\"\n        },\n        // 侧边栏\n        sidebar: {\n            categories: \"Coin Categories\",\n            allCoin: \"All Coin\"\n        },\n        // 分类翻译\n        category: {\n            \"入门\": \"Entry Level\",\n            \"高端\": \"High End\",\n            \"收藏级\": \"Collector Grade\",\n            \"纪念币\": \"Commemorative Coins\",\n            \"古典纸币\": \"Vintage Banknotes\",\n            \"体育卡牌\": \"Sports Cards\",\n            \"游戏卡牌\": \"Trading Cards\"\n        },\n        // 首页\n        home: {\n            title: \"Featured Coins & Banknotes\",\n            subtitle: \"Curated high-value collectible coins and banknotes with real-time trading status\",\n            buyNowTab: \"Buy Now\",\n            valuationTab: \"Valuation\",\n            buyNowMode: \"Buy Now Prices\",\n            valuationMode: \"Valuation Prices\",\n            buyNowDesc: \"Current available purchase prices\",\n            valuationDesc: \"Professional valuations based on market data\",\n            viewDetails: \"View Details\",\n            startValuation: \"Start Valuation\",\n            buyNow: \"Buy Now\",\n            makeOffer: \"Make Offer\",\n            heroTitle1: \"Rare Coin Collection\",\n            heroSubtitle1: \"Historical Currency Series\",\n            heroTitle2: \"Vintage Banknotes\",\n            heroSubtitle2: \"Limited Edition Collection\",\n            heroTitle3: \"Commemorative Coins\",\n            heroSubtitle3: \"Special Issue Series\",\n            hotRecommendation: \"Hot Recommendation\",\n            weeklyPopular: \"Most popular coin categories this week\"\n        },\n        // 钱币相关\n        coin: {\n            name: \"Coin Name\",\n            year: \"Year\",\n            grade: \"Grade\",\n            price: \"Price\",\n            originalPrice: \"Original Price\",\n            estimatedValue: \"Estimated Value\",\n            purchasePrice: \"Purchase Price\",\n            status: \"Status\",\n            endTime: \"End Time\",\n            seller: \"Seller\",\n            condition: \"Condition\",\n            rarity: \"Rarity\",\n            category: \"Category\",\n            description: \"Description\",\n            images: \"Images\",\n            auctioning: \"Auctioning\",\n            buyNowStatus: \"Buy Now\",\n            available: \"Available\",\n            noReserve: \"No Reserve\",\n            details: \"Details\",\n            loading: \"Loading...\",\n            invalidId: \"Invalid item ID\",\n            notFound: \"Item not found\",\n            loadError: \"Failed to load item information\",\n            sortBy: \"Sort by\",\n            sortFeatured: \"Featured\",\n            sortPriceHigh: \"Price: High to Low\",\n            sortPriceLow: \"Price: Low to High\",\n            sortNewest: \"Newest\",\n            priceType: \"Price Type\",\n            estimatedPrice: \"Estimated Price\",\n            loadingMore: \"Loading more...\",\n            noMoreCoins: \"No more coins\",\n            valuation: \"Valuation\",\n            buyNow: \"Buy Now\",\n            higherThan: \"Higher than\",\n            lowerThan: \"Lower than\",\n            purchasePrice: \"Purchase Price\",\n            priceBasedOnMarket: \"Prices based on current market conditions, updated daily\",\n            clickToView: \"Click to view larger image\",\n            loading: \"Loading...\",\n            loadingInfo: \"Loading collection information...\",\n            qualityAssurance: \"Quality Assurance\",\n            qualityDescription: \"All banknotes are professionally graded and certified to ensure quality and authenticity. We promise that every coin or banknote undergoes strict quality inspection to provide you with the most reliable collecting experience.\",\n            qualityGuarantee: \"Quality Guarantee\",\n            strictQualityStandards: \"Strict quality inspection standards\",\n            professionalService: \"Professional Service\",\n            itemDescription: \"Item Description\",\n            noDescription: \"No description available\",\n            collectionAdvice: \"Collection Advice\",\n            collectionTip: \"This coin or banknote is a rare collectible. It is recommended to store it properly, avoiding direct sunlight and humid environments.\",\n            detailedSpecs: \"Detailed Specifications\",\n            noSpecsInfo: \"No detailed specification information available\",\n            issueYear: \"Issue Year\",\n            unknown: \"Unknown\",\n            gradeLevel: \"Grade Level\",\n            grading: \"Grading\",\n            status: \"Status\",\n            conditionDesc: \"Condition Description\",\n            excellent: \"Excellent\",\n            afterSalesService: \"After-sales Service\",\n            professionalSupport: \"Professional customer service team support\",\n            professionalCertification: \"Professional Certification\",\n            authorityCertification: \"Authoritative institution grading certification\",\n            backToHome: \"Back to Home\",\n            itemDetails: \"Item Details\",\n            leftImageArea: \"Image Area\",\n            rightInfoArea: \"Product Information Area\",\n            topTitleFavorite: \"Title and Favorite Button\",\n            statusLabel: \"Status Label\",\n            middlePriceInfo: \"Price Information\",\n            save: \"Save\",\n            additionalProductInfo: \"Additional Product Information\",\n            bottomActionButtons: \"Action Buttons\",\n            valuationPageLink: \"Valuation Page Link\",\n            qualityAssuranceBlock: \"Quality Assurance Block\",\n            guaranteeFeatures: \"Guarantee Features\",\n            detailedInfo: \"Detailed Information\"\n        },\n        // 时间相关\n        time: {\n            days: \"Days\",\n            day: \"day\",\n            hours: \"Hours\",\n            minutes: \"Min\",\n            seconds: \"Sec\",\n            expired: \"Expired\",\n            expiringSoon: \"Valuation expiring soon, please process in time\",\n            endsIn: \"Ends in\",\n            hoursLeft: \"hours left\",\n            dayLeft: \"day left\",\n            daysLeft: \"days left\",\n            ended: \"Ended\"\n        },\n        // 估值页面\n        valuation: {\n            title: \"Valuation\",\n            buyPrice: \"Buy Price\",\n            sellPrice: \"Sell Price\",\n            remainingTime: \"Time Remaining\",\n            duration: \"Sell Duration\",\n            expectedPrice: \"Expected Sell Price\",\n            expectedProfit: \"Expected Profit\",\n            customPrice: \"Custom Buy Price\",\n            fixedPrice: \"Fixed Price\",\n            fixedPriceSection: \"Fixed Price\",\n            fixedPriceSetting: \"Fixed Price Setting\",\n            estimatedSellPrice: \"Estimated Sell Price\",\n            estimatedSellPriceDesc: \"Estimated sell price based on fixed price and sell multiplier\",\n            estimatedProfit: \"Estimated Profit\",\n            expectedReturn: \"Expected Return\",\n            higherReturn: \"Higher Returns\",\n            priceOptions: \"Price Options\",\n            oneDayOption: \"1 Day Sale (Stable Returns)\",\n            twoDayOption: \"2 Day Sale (Higher Potential)\",\n            startValuation: \"Start Valuation\",\n            valuationExpired: \"Valuation Expired\",\n            valuationExpiring: \"Valuation expiring soon, please process in time\",\n            includesFees: \"Includes Fees\",\n            usageGuide: \"Usage Guide\",\n            buyPriceGuide: \"Buy Price Setting\",\n            buyPriceDesc: \"Set the maximum price you are willing to pay for this coin. The system will match you with the best available price based on market conditions.\",\n            sellPriceGuide: \"Sell Price Estimation\",\n            sellPriceDesc: \"Estimated selling price based on market trends for the selected time period to help you make the best investment decisions.\",\n            // API错误信息\n            cardNotFound: \"Card not found\",\n            getValuationFailed: \"Failed to get valuation information\",\n            missingParameters: \"Missing required parameters\",\n            submitValuationFailed: \"Failed to submit valuation\",\n            submitValuationSuccess: \"Valuation submitted successfully\"\n        },\n        // WhatsApp客服\n        whatsapp: {\n            contactUs: \"Contact Us via WhatsApp\",\n            tooltip: \"Need help? Chat with us on WhatsApp!\",\n            defaultMessage: \"Hello! I would like to inquire about your coins and banknotes.\"\n        }\n    },\n    zh: {\n        // 通用\n        common: {\n            loading: \"加载中...\",\n            error: \"错误\",\n            success: \"成功\",\n            cancel: \"取消\",\n            confirm: \"确认\",\n            save: \"保存\",\n            delete: \"删除\",\n            edit: \"编辑\",\n            view: \"查看\",\n            back: \"返回\",\n            next: \"下一步\",\n            previous: \"上一步\",\n            search: \"搜索\",\n            filter: \"筛选\",\n            sort: \"排序\",\n            more: \"更多\"\n        },\n        // 导航\n        nav: {\n            home: \"首页\",\n            allCoin: \"所有古币\",\n            auction: \"拍卖\",\n            premium: \"精品\",\n            buyNow: \"立即购买\",\n            sportsCards: \"纪念币\",\n            tradingCards: \"古典纸币\",\n            valuation: \"估值功能\",\n            admin: \"管理后台\",\n            about: \"公司简介\"\n        },\n        // Header\n        header: {\n            searchPlaceholder: \"搜索古币、纸币等...\",\n            menu: \"菜单\"\n        },\n        // 公司简介页面\n        about: {\n            title: \"公司简介\",\n            subtitle: \"myduitlama 是一家专注于马来西亚及世界各国古钱币、旧纸币收藏、交易与拍卖的平台。我们致力于为每一位收藏爱好者和投资者，提供真实、专业、高品质的收藏品和服务。\",\n            aboutUsTitle: \"关于我们\",\n            aboutUsContent1: \"我们专注于旧纸币与硬币的收购、销售与拍卖，同时提供国际权威评级（如 PMG、PCGS）的咨询服务，并可代客户委托拍卖高价值藏品。\",\n            aboutUsContent2: \"我们拥有覆盖中国、美国、英国、日本及其他主要国家的买卖与拍卖交易渠道，致力于分享收藏知识与市场行情，帮助客户在收藏与投资中做出更明智的决策。\",\n            professionalPlatform: \"专业收藏平台\",\n            trustedExpert: \"值得信赖的古币旧钞交易专家\",\n            contactUs: \"联系我们\",\n            contactInfo: \"联系方式\",\n            email: \"邮箱\",\n            phone: \"电话\"\n        },\n        // 侧边栏\n        sidebar: {\n            categories: \"古币分类\",\n            allCoin: \"所有古币\"\n        },\n        // 分类翻译\n        category: {\n            \"入门\": \"入门\",\n            \"高端\": \"高端\",\n            \"收藏级\": \"收藏级\",\n            \"纪念币\": \"纪念币\",\n            \"古典纸币\": \"古典纸币\",\n            \"体育卡牌\": \"体育卡牌\",\n            \"游戏卡牌\": \"游戏卡牌\"\n        },\n        // 首页\n        home: {\n            title: \"精选古币纸币\",\n            subtitle: \"精选高价值收藏古币纸币，实时交易状态\",\n            buyNowTab: \"立即购买\",\n            valuationTab: \"估值功能\",\n            buyNowMode: \"购买价格\",\n            valuationMode: \"估值价格\",\n            buyNowDesc: \"当前可购买价格\",\n            valuationDesc: \"基于市场数据的专业估值\",\n            viewDetails: \"查看详情\",\n            startValuation: \"开始估值\",\n            buyNow: \"立即购买\",\n            makeOffer: \"出价\",\n            heroTitle1: \"稀有古币收藏\",\n            heroSubtitle1: \"历史货币系列\",\n            heroTitle2: \"古典纸币\",\n            heroSubtitle2: \"限量版收藏\",\n            heroTitle3: \"纪念币系列\",\n            heroSubtitle3: \"特别发行版\",\n            hotRecommendation: \"热门推荐\",\n            weeklyPopular: \"本周最受欢迎的钱币类别\"\n        },\n        // 钱币相关\n        coin: {\n            name: \"钱币名称\",\n            year: \"年份\",\n            grade: \"评级\",\n            price: \"价格\",\n            originalPrice: \"原价\",\n            estimatedValue: \"市场估值\",\n            purchasePrice: \"购买价格\",\n            status: \"状态\",\n            endTime: \"结束时间\",\n            seller: \"卖家\",\n            condition: \"品相\",\n            rarity: \"稀有度\",\n            category: \"分类\",\n            description: \"描述\",\n            images: \"图片\",\n            auctioning: \"竞拍中\",\n            buyNowStatus: \"立即购买\",\n            available: \"可购买\",\n            noReserve: \"无底价\",\n            details: \"商品详情\",\n            loading: \"加载中...\",\n            invalidId: \"无效的物品ID\",\n            notFound: \"未找到物品\",\n            loadError: \"加载物品信息失败\",\n            sortBy: \"排序方式\",\n            sortFeatured: \"推荐\",\n            sortPriceHigh: \"价格：从高到低\",\n            sortPriceLow: \"价格：从低到高\",\n            sortNewest: \"最新\",\n            priceType: \"价格类型\",\n            estimatedPrice: \"估值价格\",\n            loadingMore: \"加载更多...\",\n            noMoreCoins: \"没有更多钱币了\",\n            valuation: \"估值\",\n            buyNow: \"立即购买\",\n            higherThan: \"高于\",\n            lowerThan: \"低于\",\n            purchasePrice: \"购买价\",\n            priceBasedOnMarket: \"价格基于当前市场行情，每日更新\",\n            clickToView: \"点击查看大图\",\n            loading: \"加载中...\",\n            loadingInfo: \"加载藏品信息中...\",\n            qualityAssurance: \"品质保证\",\n            qualityDescription: \"所有旧钞均经过专业评级认证，确保品质和真实性。我们承诺每一枚古币或旧钞都经过严格的质量检验，为您提供最可靠的收藏体验。\",\n            qualityGuarantee: \"品质保障\",\n            strictQualityStandards: \"严格质量检验标准\",\n            professionalService: \"专业服务\",\n            itemDescription: \"藏品描述\",\n            noDescription: \"暂无描述信息\",\n            collectionAdvice: \"收藏建议\",\n            collectionTip: \"此古币或旧钞属于稀有收藏品，建议妥善保存，避免阳光直射和潮湿环境。\",\n            detailedSpecs: \"详细规格\",\n            noSpecsInfo: \"暂无详细规格信息\",\n            issueYear: \"发行年份\",\n            unknown: \"未知\",\n            gradeLevel: \"品相等级\",\n            grading: \"评级中\",\n            status: \"状态\",\n            conditionDesc: \"品相描述\",\n            excellent: \"优秀\",\n            afterSalesService: \"售后服务\",\n            professionalSupport: \"专业客服团队支持\",\n            professionalCertification: \"专业认证\",\n            authorityCertification: \"权威机构评级认证\",\n            backToHome: \"返回首页\",\n            itemDetails: \"藏品详情\",\n            leftImageArea: \"左侧：图片区域\",\n            rightInfoArea: \"右侧：商品信息区域\",\n            topTitleFavorite: \"顶部：标题和收藏按钮\",\n            statusLabel: \"状态标签\",\n            middlePriceInfo: \"中间：价格信息 - 使用flex-1让这部分占据剩余空间\",\n            save: \"节省\",\n            additionalProductInfo: \"添加一些额外的商品信息来填充空间\",\n            bottomActionButtons: \"底部：操作按钮\",\n            valuationPageLink: \"估值页面链接\",\n            qualityAssuranceBlock: \"品质保证区块 - 单独一行\",\n            guaranteeFeatures: \"添加一些保证特性\",\n            detailedInfo: \"详细信息\"\n        },\n        // 时间相关\n        time: {\n            days: \"天\",\n            day: \"天\",\n            hours: \"小时\",\n            minutes: \"分\",\n            seconds: \"秒\",\n            expired: \"已结束\",\n            expiringSoon: \"估值即将到期，请及时处理\",\n            endsIn: \"\",\n            hoursLeft: \"小时后结束\",\n            dayLeft: \"天后结束\",\n            daysLeft: \"天后结束\",\n            ended: \"已结束\"\n        },\n        // 估值页面\n        valuation: {\n            title: \"估值页面\",\n            buyPrice: \"买入价格\",\n            sellPrice: \"抛售价格\",\n            remainingTime: \"剩余时间\",\n            duration: \"抛售期限\",\n            expectedPrice: \"预期抛售价格\",\n            expectedProfit: \"预期收益\",\n            customPrice: \"自定义买入价格\",\n            fixedPrice: \"固定价格\",\n            fixedPriceSection: \"固定价格\",\n            fixedPriceSetting: \"固定价格设置\",\n            estimatedSellPrice: \"预估抛售价格\",\n            estimatedSellPriceDesc: \"基于固定价格和抛售系数计算的预估抛售价格\",\n            estimatedProfit: \"预估利润\",\n            expectedReturn: \"预期收益\",\n            higherReturn: \"收益更高\",\n            priceOptions: \"价格选项\",\n            oneDayOption: \"1天后抛售 (稳健收益)\",\n            twoDayOption: \"2天后抛售 (高收益潜力)\",\n            startValuation: \"开始估值\",\n            valuationExpired: \"估值已过期\",\n            valuationExpiring: \"估值即将过期，请及时处理\",\n            includesFees: \"含手续费\",\n            usageGuide: \"使用指南\",\n            buyPriceGuide: \"买入价格设置\",\n            buyPriceDesc: \"设置您愿意购买此钱币的最高价格，系统会根据市场情况为您匹配最优价格。\",\n            sellPriceGuide: \"抛售价格预估\",\n            sellPriceDesc: \"根据市场趋势预估在选定时间后出售可获得的价格，帮助您做出最佳投资决策。\",\n            // API错误信息\n            cardNotFound: \"卡牌不存在\",\n            getValuationFailed: \"获取估值信息失败\",\n            missingParameters: \"缺少必要参数\",\n            submitValuationFailed: \"提交估值失败\",\n            submitValuationSuccess: \"估值提交成功\"\n        },\n        // WhatsApp客服\n        whatsapp: {\n            contactUs: \"通过WhatsApp联系我们\",\n            tooltip: \"需要帮助？在WhatsApp上与我们聊天！\",\n            defaultMessage: \"您好！我想咨询一下您的古币纸币。\"\n        }\n    },\n    ms: {\n        // 通用\n        common: {\n            loading: \"Memuatkan...\",\n            error: \"Ralat\",\n            success: \"Berjaya\",\n            cancel: \"Batal\",\n            confirm: \"Sahkan\",\n            save: \"Simpan\",\n            delete: \"Padam\",\n            edit: \"Edit\",\n            view: \"Lihat\",\n            back: \"Kembali\",\n            next: \"Seterusnya\",\n            previous: \"Sebelumnya\",\n            search: \"Cari\",\n            filter: \"Tapis\",\n            sort: \"Susun\",\n            more: \"Lagi\"\n        },\n        // 导航\n        nav: {\n            home: \"Laman Utama\",\n            allCoin: \"Semua Syiling\",\n            auction: \"Lelongan\",\n            premium: \"Premium\",\n            buyNow: \"Beli Sekarang\",\n            sportsCards: \"Syiling Peringatan\",\n            tradingCards: \"Wang Kertas Vintaj\",\n            valuation: \"Penilaian\",\n            admin: \"Admin\",\n            about: \"Tentang Kami\"\n        },\n        // Header\n        header: {\n            searchPlaceholder: \"Cari syiling, wang kertas, dll...\",\n            menu: \"Menu\"\n        },\n        // 公司简介页面\n        about: {\n            title: \"Tentang Kami\",\n            subtitle: \"myduitlama adalah platform yang memfokuskan kepada pengumpulan, perdagangan dan lelongan syiling purba dan wang kertas lama dari Malaysia dan negara-negara di seluruh dunia. Kami komited untuk menyediakan koleksi dan perkhidmatan yang tulen, profesional dan berkualiti tinggi kepada setiap pengumpul dan pelabur.\",\n            aboutUsTitle: \"Tentang Kami\",\n            aboutUsContent1: \"Kami pakar dalam pemerolehan, penjualan dan lelongan wang kertas lama dan syiling, sambil menyediakan perkhidmatan perundingan untuk penggredan berwibawa antarabangsa (seperti PMG, PCGS), dan boleh menugaskan lelongan koleksi bernilai tinggi bagi pihak pelanggan.\",\n            aboutUsContent2: \"Kami mempunyai saluran perdagangan dan lelongan yang meliputi China, Amerika Syarikat, United Kingdom, Jepun dan negara-negara utama lain, dan komited untuk berkongsi pengetahuan pengumpulan dan keadaan pasaran untuk membantu pelanggan membuat keputusan yang lebih termaklum dalam pengumpulan dan pelaburan.\",\n            professionalPlatform: \"Platform Koleksi Profesional\",\n            trustedExpert: \"Pakar Perdagangan Syiling Purba dan Wang Kertas Lama yang Dipercayai\",\n            contactUs: \"Hubungi Kami\",\n            contactInfo: \"Maklumat Hubungan\",\n            email: \"E-mel\",\n            phone: \"Telefon\"\n        },\n        // 侧边栏\n        sidebar: {\n            categories: \"Kategori Syiling\",\n            allCoin: \"Semua Syiling\"\n        },\n        // 分类翻译\n        category: {\n            \"入门\": \"Tahap Permulaan\",\n            \"高端\": \"Tahap Tinggi\",\n            \"收藏级\": \"Gred Koleksi\",\n            \"纪念币\": \"Syiling Peringatan\",\n            \"古典纸币\": \"Wang Kertas Klasik\",\n            \"体育卡牌\": \"Kad Sukan\",\n            \"游戏卡牌\": \"Kad Permainan\"\n        },\n        // 首页\n        home: {\n            title: \"Syiling & Wang Kertas Pilihan\",\n            subtitle: \"Syiling dan wang kertas koleksi bernilai tinggi yang dipilih dengan status dagangan masa nyata\",\n            buyNowTab: \"Beli Sekarang\",\n            valuationTab: \"Penilaian\",\n            buyNowMode: \"Harga Beli Sekarang\",\n            valuationMode: \"Harga Penilaian\",\n            buyNowDesc: \"Harga pembelian semasa yang tersedia\",\n            valuationDesc: \"Penilaian profesional berdasarkan data pasaran\",\n            viewDetails: \"Lihat Butiran\",\n            startValuation: \"Mula Penilaian\",\n            buyNow: \"Beli Sekarang\",\n            makeOffer: \"Buat Tawaran\",\n            heroTitle1: \"Koleksi Syiling Jarang\",\n            heroSubtitle1: \"Siri Mata Wang Bersejarah\",\n            heroTitle2: \"Wang Kertas Vintaj\",\n            heroSubtitle2: \"Koleksi Edisi Terhad\",\n            heroTitle3: \"Syiling Peringatan\",\n            heroSubtitle3: \"Siri Terbitan Khas\",\n            hotRecommendation: \"Cadangan Popular\",\n            weeklyPopular: \"Kategori syiling paling popular minggu ini\"\n        },\n        // 钱币相关\n        coin: {\n            name: \"Nama Syiling\",\n            year: \"Tahun\",\n            grade: \"Gred\",\n            price: \"Harga\",\n            originalPrice: \"Harga Asal\",\n            estimatedValue: \"Nilai Anggaran\",\n            status: \"Status\",\n            endTime: \"Masa Tamat\",\n            seller: \"Penjual\",\n            condition: \"Keadaan\",\n            rarity: \"Kekurangan\",\n            category: \"Kategori\",\n            description: \"Penerangan\",\n            images: \"Gambar\",\n            auctioning: \"Dalam Lelongan\",\n            buyNowStatus: \"Beli Sekarang\",\n            available: \"Tersedia untuk Penilaian\",\n            noReserve: \"Tiada Rizab\",\n            details: \"Butiran Kad\",\n            loading: \"Memuatkan...\",\n            invalidId: \"ID item tidak sah\",\n            notFound: \"Item tidak dijumpai\",\n            loadError: \"Gagal memuatkan maklumat item\",\n            sortBy: \"Susun mengikut\",\n            sortFeatured: \"Pilihan\",\n            sortPriceHigh: \"Harga: Tinggi ke Rendah\",\n            sortPriceLow: \"Harga: Rendah ke Tinggi\",\n            sortNewest: \"Terbaru\",\n            priceType: \"Jenis Harga\",\n            purchasePrice: \"Harga Pembelian\",\n            estimatedPrice: \"Harga Anggaran\",\n            loadingMore: \"Memuatkan lagi...\",\n            noMoreCoins: \"Tiada lagi syiling\",\n            valuation: \"Penilaian\",\n            buyNow: \"Beli Sekarang\",\n            higherThan: \"Lebih tinggi daripada\",\n            lowerThan: \"Lebih rendah daripada\",\n            purchasePrice: \"Harga Pembelian\",\n            priceBasedOnMarket: \"Harga berdasarkan keadaan pasaran semasa, dikemas kini setiap hari\",\n            clickToView: \"Klik untuk melihat gambar yang lebih besar\",\n            loading: \"Memuatkan...\",\n            loadingInfo: \"Memuatkan maklumat koleksi...\",\n            qualityAssurance: \"Jaminan Kualiti\",\n            qualityDescription: \"Semua wang kertas lama telah melalui pensijilan gred profesional untuk memastikan kualiti dan keaslian. Kami berjanji bahawa setiap syiling atau wang kertas melalui pemeriksaan kualiti yang ketat untuk memberikan anda pengalaman mengumpul yang paling boleh dipercayai.\",\n            qualityGuarantee: \"Jaminan Kualiti\",\n            strictQualityStandards: \"Standard pemeriksaan kualiti yang ketat\",\n            professionalService: \"Perkhidmatan Profesional\",\n            itemDescription: \"Penerangan Item\",\n            noDescription: \"Tiada penerangan tersedia\",\n            collectionAdvice: \"Nasihat Koleksi\",\n            collectionTip: \"Syiling atau wang kertas ini adalah koleksi yang jarang ditemui. Disyorkan untuk menyimpannya dengan betul, mengelakkan cahaya matahari langsung dan persekitaran lembap.\",\n            detailedSpecs: \"Spesifikasi Terperinci\",\n            noSpecsInfo: \"Tiada maklumat spesifikasi terperinci tersedia\",\n            issueYear: \"Tahun Terbitan\",\n            unknown: \"Tidak Diketahui\",\n            gradeLevel: \"Tahap Gred\",\n            grading: \"Dalam Penggredan\",\n            status: \"Status\",\n            conditionDesc: \"Penerangan Keadaan\",\n            excellent: \"Cemerlang\",\n            afterSalesService: \"Perkhidmatan Selepas Jualan\",\n            professionalSupport: \"Sokongan pasukan khidmat pelanggan profesional\",\n            professionalCertification: \"Pensijilan Profesional\",\n            authorityCertification: \"Pensijilan gred institusi berwibawa\",\n            backToHome: \"Kembali ke Laman Utama\",\n            itemDetails: \"Butiran Item\",\n            leftImageArea: \"Kawasan Gambar\",\n            rightInfoArea: \"Kawasan Maklumat Produk\",\n            topTitleFavorite: \"Tajuk dan Butang Kegemaran\",\n            statusLabel: \"Label Status\",\n            middlePriceInfo: \"Maklumat Harga\",\n            save: \"Jimat\",\n            additionalProductInfo: \"Maklumat Produk Tambahan\",\n            bottomActionButtons: \"Butang Tindakan\",\n            valuationPageLink: \"Pautan Halaman Penilaian\",\n            qualityAssuranceBlock: \"Blok Jaminan Kualiti\",\n            guaranteeFeatures: \"Ciri-ciri Jaminan\",\n            detailedInfo: \"Maklumat Terperinci\"\n        },\n        // 时间相关\n        time: {\n            days: \"Hari\",\n            day: \"hari\",\n            hours: \"Jam\",\n            minutes: \"Min\",\n            seconds: \"Saat\",\n            expired: \"Tamat\",\n            expiringSoon: \"Penilaian akan tamat tidak lama lagi, sila proses dengan segera\",\n            endsIn: \"Berakhir dalam\",\n            hoursLeft: \"jam lagi\",\n            dayLeft: \"hari lagi\",\n            daysLeft: \"hari lagi\",\n            ended: \"Tamat\"\n        },\n        // 估值页面\n        valuation: {\n            title: \"Halaman Penilaian\",\n            buyPrice: \"Harga Beli\",\n            sellPrice: \"Harga Jual\",\n            remainingTime: \"Masa Berbaki\",\n            duration: \"Tempoh Jualan\",\n            expectedPrice: \"Harga Jualan Dijangka\",\n            expectedProfit: \"Keuntungan Dijangka\",\n            customPrice: \"Harga Beli Tersuai\",\n            fixedPrice: \"Harga Tetap\",\n            fixedPriceSection: \"Harga Tetap\",\n            fixedPriceSetting: \"Tetapan Harga Tetap\",\n            estimatedSellPrice: \"Harga Jual Anggaran\",\n            estimatedSellPriceDesc: \"Harga jual anggaran berdasarkan harga tetap dan pekali jualan\",\n            estimatedProfit: \"Keuntungan Anggaran\",\n            expectedReturn: \"Pulangan Dijangka\",\n            higherReturn: \"Pulangan Lebih Tinggi\",\n            priceOptions: \"Pilihan Harga\",\n            oneDayOption: \"Jualan 1 Hari (Pulangan Stabil)\",\n            twoDayOption: \"Jualan 2 Hari (Potensi Lebih Tinggi)\",\n            startValuation: \"Mula Penilaian\",\n            valuationExpired: \"Penilaian Tamat Tempoh\",\n            valuationExpiring: \"Penilaian akan tamat tidak lama lagi, sila proses dengan segera\",\n            includesFees: \"Termasuk Yuran\",\n            usageGuide: \"Panduan Penggunaan\",\n            buyPriceGuide: \"Tetapan Harga Beli\",\n            buyPriceDesc: \"Tetapkan harga maksimum yang anda sanggup bayar untuk syiling ini. Sistem akan memadankan anda dengan harga terbaik berdasarkan keadaan pasaran.\",\n            sellPriceGuide: \"Anggaran Harga Jualan\",\n            sellPriceDesc: \"Harga jualan anggaran berdasarkan trend pasaran untuk tempoh masa yang dipilih untuk membantu anda membuat keputusan pelaburan terbaik.\",\n            // API错误信息\n            cardNotFound: \"Kad tidak dijumpai\",\n            getValuationFailed: \"Gagal mendapatkan maklumat penilaian\",\n            missingParameters: \"Parameter yang diperlukan hilang\",\n            submitValuationFailed: \"Gagal menghantar penilaian\",\n            submitValuationSuccess: \"Penilaian berjaya dihantar\"\n        },\n        // WhatsApp客服\n        whatsapp: {\n            contactUs: \"Hubungi Kami melalui WhatsApp\",\n            tooltip: \"Perlukan bantuan? Sembang dengan kami di WhatsApp!\",\n            defaultMessage: \"Hello! Saya ingin bertanya tentang syiling dan wang kertas anda.\"\n        }\n    }\n};\n// 当前语言设置\nlet currentLanguage = \"en\"; // 默认英文\n// 获取翻译文本\nfunction t(key, lang) {\n    // 如果没有指定语言，使用当前语言\n    if (!lang) {\n        lang = getCurrentLanguage();\n    }\n    const keys = key.split(\".\");\n    let value = translations[lang];\n    for (const k of keys){\n        if (value && typeof value === \"object\") {\n            value = value[k];\n        } else {\n            return key; // 如果找不到翻译，返回原key\n        }\n    }\n    return value || key;\n}\n// 格式化时间显示\nfunction formatTimeLeft(timeString, lang) {\n    if (!timeString) return \"\";\n    // 如果没有指定语言，使用当前语言\n    if (!lang) {\n        lang = getCurrentLanguage();\n    }\n    // 解析时间字符串，支持多种格式\n    const match = timeString.match(/(\\d+)(小时|天|hour|day|jam|hari)/i);\n    if (!match) return timeString; // 如果无法解析，返回原字符串\n    const number = parseInt(match[1]);\n    const unit = match[2].toLowerCase();\n    // 根据语言和单位返回格式化的时间\n    if (lang === \"zh\") {\n        if (unit.includes(\"小时\") || unit.includes(\"hour\") || unit.includes(\"jam\")) {\n            return `${number}${t(\"time.hoursLeft\", lang)}`;\n        } else if (unit.includes(\"天\") || unit.includes(\"day\") || unit.includes(\"hari\")) {\n            return number === 1 ? `${number}${t(\"time.dayLeft\", lang)}` : `${number}${t(\"time.daysLeft\", lang)}`;\n        }\n    } else if (lang === \"en\") {\n        if (unit.includes(\"小时\") || unit.includes(\"hour\") || unit.includes(\"jam\")) {\n            return `${number} ${t(\"time.hoursLeft\", lang)}`;\n        } else if (unit.includes(\"天\") || unit.includes(\"day\") || unit.includes(\"hari\")) {\n            return number === 1 ? `${number} ${t(\"time.dayLeft\", lang)}` : `${number} ${t(\"time.daysLeft\", lang)}`;\n        }\n    } else if (lang === \"ms\") {\n        if (unit.includes(\"小时\") || unit.includes(\"hour\") || unit.includes(\"jam\")) {\n            return `${t(\"time.endsIn\", lang)} ${number} ${t(\"time.hoursLeft\", lang)}`;\n        } else if (unit.includes(\"天\") || unit.includes(\"day\") || unit.includes(\"hari\")) {\n            return number === 1 ? `${t(\"time.endsIn\", lang)} ${number} ${t(\"time.dayLeft\", lang)}` : `${t(\"time.endsIn\", lang)} ${number} ${t(\"time.daysLeft\", lang)}`;\n        }\n    }\n    return timeString; // 如果无法处理，返回原字符串\n}\n// 设置语言\nfunction setLanguage(lang) {\n    if (translations[lang]) {\n        currentLanguage = lang;\n        // 可以在这里添加本地存储\n        if (false) {}\n    }\n}\n// 获取当前语言\nfunction getCurrentLanguage() {\n    if (false) {}\n    return currentLanguage;\n}\n// 获取所有可用语言\nfunction getAvailableLanguages() {\n    return Object.keys(translations);\n}\n// 获取分类名称的翻译\nfunction getCategoryName(categoryName, lang) {\n    var ref;\n    // 如果没有指定语言，使用当前语言\n    if (!lang) {\n        lang = getCurrentLanguage();\n    }\n    // 尝试从翻译中获取分类名称\n    const categoryTranslations = (ref = translations[lang]) === null || ref === void 0 ? void 0 : ref.category;\n    if (categoryTranslations && categoryTranslations[categoryName]) {\n        return categoryTranslations[categoryName];\n    }\n    // 如果没有找到翻译，返回原名称\n    return categoryName;\n}\n// 初始化语言设置\nfunction initializeLanguage() {\n    if (false) {}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/i18n.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/useAuth */ \"./hooks/useAuth.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/i18n */ \"./lib/i18n.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_WhatsAppButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/WhatsAppButton */ \"./components/WhatsAppButton.js\");\n\n\n\n\n\n\n\n\nfunction App({ Component , pageProps  }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const isAdminRoute = router.pathname.startsWith(\"/admin\");\n    const { 0: siteConfig , 1: setSiteConfig  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    // 初始化语言设置\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_5__.initializeLanguage)();\n    }, []);\n    // 获取网站配置（包括WhatsApp设置）\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        let isMounted = true; // 添加挂载状态标记\n        const fetchSiteConfig = async ()=>{\n            try {\n                const response = await fetch(\"/api/site-config\");\n                if (response.ok && isMounted) {\n                    const config = await response.json();\n                    setSiteConfig(config);\n                }\n            } catch (error) {\n                if (isMounted) {\n                    console.error(\"Error fetching site config:\", error);\n                }\n            }\n        };\n        // 只在非管理后台页面获取配置\n        if (!isAdminRoute) {\n            fetchSiteConfig();\n        }\n        // 清理函数\n        return ()=>{\n            isMounted = false;\n        };\n    }, [\n        isAdminRoute\n    ]);\n    const AppContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            href: \"https://fonts.googleapis.com/css2?family=Pacifico&display=swap\",\n                            rel: \"stylesheet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_app.js\",\n                            lineNumber: 51,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            href: \"https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.5.0/remixicon.min.css\",\n                            rel: \"stylesheet\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_app.js\",\n                            lineNumber: 52,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_app.js\",\n                    lineNumber: 50,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_app.js\",\n                    lineNumber: 54,\n                    columnNumber: 7\n                }, this),\n                !isAdminRoute && siteConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WhatsAppButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    whatsappConfig: {\n                        enabled: siteConfig.whatsapp_enabled,\n                        phone: siteConfig.whatsapp_phone,\n                        message: siteConfig.whatsapp_message,\n                        tooltip: siteConfig.whatsapp_tooltip\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_app.js\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    // 只在管理后台路由中使用AuthProvider，但排除登录页面\n    if (isAdminRoute && router.pathname !== \"/admin/login\" && router.pathname !== \"/admin/simple-login\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContent, {}, void 0, false, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_app.js\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_app.js\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContent, {}, void 0, false, {\n        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\_app.js\",\n        lineNumber: 79,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "next/router":
/*!******************************!*\
  !*** external "next/router" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/router");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.js"));
module.exports = __webpack_exports__;

})();