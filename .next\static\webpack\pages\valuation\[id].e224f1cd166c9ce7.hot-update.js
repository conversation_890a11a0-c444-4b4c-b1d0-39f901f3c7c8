"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/valuation/[id]",{

/***/ "./pages/valuation/[id].js":
/*!*********************************!*\
  !*** ./pages/valuation/[id].js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ValuationPage; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/Header */ \"./components/Header.js\");\n/* harmony import */ var _components_ImageGallery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ImageGallery */ \"./components/ImageGallery.js\");\n/* harmony import */ var _components_CountdownTimer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/CountdownTimer */ \"./components/CountdownTimer.js\");\n/* harmony import */ var _hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../hooks/useSiteConfig */ \"./hooks/useSiteConfig.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../lib/i18n */ \"./lib/i18n.js\");\n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ValuationPage() {\n    _s();\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    var id = router.query.id;\n    var ref = (0,_hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__.useSiteConfig)(), siteConfig = ref.siteConfig, configLoading = ref.loading;\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), card = ref1[0], setCard = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), loading = ref2[0], setLoading = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), error = ref3[0], setError = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), buyPrice = ref4[0], setBuyPrice = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), sellPrice = ref5[0], setSellPrice = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1day\"), selectedDuration = ref6[0], setSelectedDuration = ref6[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (id) {\n            loadCard();\n        }\n    }, [\n        id\n    ]);\n    function loadCard() {\n        return _loadCard.apply(this, arguments);\n    }\n    function _loadCard() {\n        _loadCard = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            var response, cardData, fixedPrice, sellMultiplier, error;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            3,\n                            4,\n                            5\n                        ]);\n                        setLoading(true);\n                        return [\n                            4,\n                            fetch(\"/api/cards/\".concat(id))\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) {\n                            if (response.status === 404) {\n                                setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                            } else {\n                                setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                            }\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 2:\n                        cardData = _state.sent();\n                        setCard(cardData);\n                        fixedPrice = cardData.estimatedValue || cardData.price;\n                        sellMultiplier = cardData.sellMultiplier || 1.050;\n                        setBuyPrice(fixedPrice.toFixed(2));\n                        setSellPrice((fixedPrice * sellMultiplier).toFixed(2));\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error loading card:\", error);\n                        setError((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return _loadCard.apply(this, arguments);\n    }\n    var handleSubmitValuation = function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            var response, _$error, error;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            5,\n                            ,\n                            6\n                        ]);\n                        return [\n                            4,\n                            fetch(\"/api/valuation/\".concat(id), {\n                                method: \"POST\",\n                                headers: {\n                                    \"Content-Type\": \"application/json\"\n                                },\n                                body: JSON.stringify({\n                                    buyPrice: buyPrice,\n                                    sellPrice: sellPrice,\n                                    duration: selectedDuration\n                                })\n                            })\n                        ];\n                    case 1:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            2\n                        ];\n                        alert(\"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.success\"), \"!\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPrice\"), \": RM\").concat(buyPrice, \"\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPrice\"), \": RM\").concat(sellPrice, \"\\n\").concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.duration\"), \": \").concat(selectedDuration === \"1day\" ? (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.oneDayOption\") : (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.twoDayOption\")));\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 3:\n                        _$error = _state.sent();\n                        alert(\"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"), \": \").concat(_$error.error || (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\")));\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            3,\n                            6\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        console.error(\"Error submitting valuation:\", error);\n                        alert((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\"));\n                        return [\n                            3,\n                            6\n                        ];\n                    case 6:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function handleSubmitValuation() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    if (loading || configLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    siteConfig: siteConfig\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !card) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    siteConfig: siteConfig\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl text-gray-400 mb-4\",\n                                    children: \"\\uD83D\\uDE1E\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                    children: error || (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.error\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"text-blue-600 hover:text-blue-800 transition-colors duration-200\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"common.back\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: [\n                            card.name,\n                            \" - \",\n                            (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\"),\n                            \" - \",\n                            siteConfig.title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"\".concat((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.title\"), \" - \").concat(card.name)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        siteConfig: siteConfig\n                    }, void 0, false, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-sm mx-auto px-4 py-4 sm:max-w-2xl sm:px-6 sm:py-6 lg:max-w-7xl lg:px-8 lg:py-8 xl:max-w-[1600px] xl:px-12 2xl:max-w-[1800px] 2xl:px-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center space-x-2 text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6 lg:mb-8 overflow-x-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            className: \"hover:text-purple-600 transition-colors duration-200 whitespace-nowrap\",\n                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.home\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-arrow-right-s-line\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/card/\".concat(card.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            className: \"hover:text-purple-600 transition-colors duration-200 whitespace-nowrap\",\n                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.details\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-arrow-right-s-line\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-purple-600 font-medium whitespace-nowrap\",\n                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 sticky top-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-square bg-gray-100 p-4 sm:p-6 lg:p-8\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full rounded-lg overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageGallery__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    images: card.images\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium\",\n                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"nav.valuation\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 sm:p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 173,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.status\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 174,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 172,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-green-600 bg-green-100 px-2 py-1 rounded-md\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.available\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 176,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.remainingTime\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-gray-900 bg-white px-2 py-1 rounded-md border\",\n                                                                            children: [\n                                                                                \"1 \",\n                                                                                (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"time.day\")\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg p-4 text-center border border-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.year\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: card.year\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg p-4 text-center border border-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mb-2 uppercase tracking-wide font-medium\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"coin.grade\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: card.grade\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 196,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-1\",\n                                                            children: card.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: card.subtitle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-900 px-6 py-4 text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-lg font-semibold text-gray-200\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.fixedPriceSection\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold mt-1\",\n                                                                        children: [\n                                                                            \"RM\",\n                                                                            parseFloat(buyPrice).toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.includesFees\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-300\",\n                                                                        children: \"(BP)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-6 py-4 bg-gray-50 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-red-600 mb-2 font-medium\",\n                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.remainingTime\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CountdownTimer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            duration: selectedDuration === \"1day\" ? 24 : 48,\n                                                            onComplete: function() {\n                                                                return alert((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.valuationExpired\"));\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-medium text-gray-700 mb-3\",\n                                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.fixedPriceSetting\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.fixedPrice\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm\",\n                                                                                    children: \"RM\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 247,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    value: buyPrice,\n                                                                                    readOnly: true,\n                                                                                    className: \"w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-900 bg-gray-50 cursor-not-allowed transition-all duration-200\",\n                                                                                    placeholder: \"1,590.00\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs\",\n                                                                                            children: \"?\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 257,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 256,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 255,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t border-gray-200 my-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.estimatedSellPrice\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600 text-xs\",\n                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.estimatedSellPriceDesc\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                    children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.duration\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 277,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    value: selectedDuration,\n                                                                                    onChange: function(e) {\n                                                                                        return setSelectedDuration(e.target.value);\n                                                                                    },\n                                                                                    className: \"w-full p-2.5 border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-900 focus:border-gray-900 text-sm bg-white transition-all duration-200\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"1day\",\n                                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.oneDayOption\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 285,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"2day\",\n                                                                                            children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.twoDayOption\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                            lineNumber: 286,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-green-50 rounded-lg p-4 mb-4 border border-green-200\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-green-700 font-medium mb-1\",\n                                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.estimatedSellPrice\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 293,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-2xl font-bold text-green-800 mb-1\",\n                                                                                        children: [\n                                                                                            \"RM\",\n                                                                                            parseFloat(sellPrice).toLocaleString()\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 294,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-green-600\",\n                                                                                        children: [\n                                                                                            (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.estimatedProfit\"),\n                                                                                            \": +RM\",\n                                                                                            parseFloat(sellPrice - buyPrice).toLocaleString()\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                        lineNumber: 297,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleSubmitValuation,\n                                                                                className: \"w-full bg-gray-900 hover:bg-gray-800 text-white py-3 px-4 rounded-md font-medium text-sm transition-all duration-200\",\n                                                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.startValuation\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white border border-gray-200 rounded-lg p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-4xl mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4 text-center\",\n                                                children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.usageGuide\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"\\uD83D\\uDCB0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPriceGuide\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.buyPriceDesc\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"\\uD83D\\uDCC8\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPriceGuide\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_9__.t)(\"valuation.sellPriceDesc\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\py-ide\\\\wudalang-shop\\\\pages\\\\valuation\\\\[id].js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ValuationPage, \"iaMa5+/wjPnPymBxeZku1OdikGM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useSiteConfig__WEBPACK_IMPORTED_MODULE_8__.useSiteConfig\n    ];\n});\n_c = ValuationPage;\nvar _c;\n$RefreshReg$(_c, \"ValuationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/valuation/[id].js\n"));

/***/ })

});