"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/cards";
exports.ids = ["pages/api/admin/cards"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "dotenv":
/*!*************************!*\
  !*** external "dotenv" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("dotenv");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "(api)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst bcrypt = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\nconst jwt = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\nconst { adminUsersDAO  } = __webpack_require__(/*! ./mysqlDatabase */ \"(api)/./lib/mysqlDatabase.js\");\n// JWT密钥，生产环境应该使用环境变量\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-in-production\";\nconst JWT_EXPIRES_IN = \"7d\"; // Token有效期7天\n/**\n * 验证用户登录\n * @param {string} username 用户名\n * @param {string} password 密码\n * @returns {Object|null} 用户信息或null\n */ async function verifyLogin(username, password) {\n    try {\n        // 查找用户\n        const user = await adminUsersDAO.getByUsername(username);\n        if (!user) {\n            return null;\n        }\n        // 验证密码\n        const isValidPassword = await bcrypt.compare(password, user.password);\n        if (!isValidPassword) {\n            return null;\n        }\n        // 更新最后登录时间\n        await adminUsersDAO.updateLastLogin(user.id);\n        // 返回用户信息（不包含密码）\n        const { password: _ , ...userWithoutPassword } = user;\n        return userWithoutPassword;\n    } catch (error) {\n        console.error(\"Error verifying login:\", error);\n        return null;\n    }\n}\n/**\n * 生成JWT Token\n * @param {Object} user 用户信息\n * @returns {string} JWT Token\n */ function generateToken(user) {\n    const payload = {\n        id: user.id,\n        username: user.username,\n        role: user.role\n    };\n    return jwt.sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\n/**\n * 验证JWT Token\n * @param {string} token JWT Token\n * @returns {Object|null} 解码后的用户信息或null\n */ function verifyToken(token) {\n    try {\n        return jwt.verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error(\"Error verifying token:\", error);\n        return null;\n    }\n}\n/**\n * 从请求中获取用户信息\n * @param {Object} req 请求对象\n * @returns {Object|null} 用户信息或null\n */ function getUserFromRequest(req) {\n    try {\n        // 从Authorization header获取token\n        const authHeader = req.headers.authorization;\n        if (authHeader && authHeader.startsWith(\"Bearer \")) {\n            const token = authHeader.substring(7);\n            return verifyToken(token);\n        }\n        // 从cookie获取token\n        let token1 = null;\n        if (req.cookies) {\n            token1 = req.cookies.adminToken;\n        } else if (req.headers.cookie) {\n            // 手动解析cookie\n            const cookies = req.headers.cookie.split(\";\").reduce((acc, cookie)=>{\n                const parts = cookie.trim().split(\"=\");\n                if (parts.length === 2) {\n                    acc[parts[0]] = parts[1];\n                }\n                return acc;\n            }, {});\n            token1 = cookies.adminToken;\n        }\n        console.log(\"Token from request:\", token1 ? \"Found\" : \"Not found\");\n        if (token1) {\n            console.log(\"Token value:\", token1.substring(0, 20) + \"...\");\n        }\n        if (token1) {\n            const verifiedUser = verifyToken(token1);\n            console.log(\"Token verification result:\", verifiedUser ? \"Valid\" : \"Invalid\");\n            return verifiedUser;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user from request:\", error);\n        return null;\n    }\n}\n/**\n * 认证中间件\n * @param {Function} handler API处理函数\n * @returns {Function} 包装后的处理函数\n */ function withAuth(handler) {\n    return async (req, res)=>{\n        try {\n            const user = getUserFromRequest(req);\n            if (!user) {\n                return res.status(401).json({\n                    message: \"未授权访问，请先登录\",\n                    code: \"UNAUTHORIZED\"\n                });\n            }\n            // 将用户信息添加到请求对象\n            req.user = user;\n            // 调用原始处理函数\n            return handler(req, res);\n        } catch (error) {\n            console.error(\"Auth middleware error:\", error);\n            return res.status(500).json({\n                message: \"服务器内部错误\",\n                code: \"INTERNAL_ERROR\"\n            });\n        }\n    };\n}\n/**\n * 检查用户是否有管理员权限\n * @param {Object} user 用户信息\n * @returns {boolean} 是否有管理员权限\n */ function isAdmin(user) {\n    return user && user.role === \"admin\";\n}\n/**\n * 管理员权限中间件\n * @param {Function} handler API处理函数\n * @returns {Function} 包装后的处理函数\n */ function withAdminAuth(handler) {\n    return withAuth(async (req, res)=>{\n        if (!isAdmin(req.user)) {\n            return res.status(403).json({\n                message: \"权限不足，需要管理员权限\",\n                code: \"FORBIDDEN\"\n            });\n        }\n        return handler(req, res);\n    });\n}\n/**\n * 密码哈希\n * @param {string} password 明文密码\n * @returns {string} 哈希后的密码\n */ async function hashPassword(password) {\n    return bcrypt.hash(password, 10);\n}\nmodule.exports = {\n    verifyLogin,\n    generateToken,\n    verifyToken,\n    getUserFromRequest,\n    withAuth,\n    withAdminAuth,\n    isAdmin,\n    hashPassword\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/auth.js\n");

/***/ }),

/***/ "(api)/./lib/mysqlDatabase.js":
/*!******************************!*\
  !*** ./lib/mysqlDatabase.js ***!
  \******************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n(__webpack_require__(/*! dotenv */ \"dotenv\").config)();\nconst mysql = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n// 数据库配置\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"cardmarket\",\n    charset: \"utf8mb4\",\n    timezone: \"+08:00\"\n};\n// 创建连接池\nlet pool;\nfunction getPool() {\n    if (!pool) {\n        pool = mysql.createPool({\n            ...dbConfig,\n            waitForConnections: true,\n            connectionLimit: 10,\n            queueLimit: 0\n        });\n    }\n    return pool;\n}\n// 执行查询\nasync function query(sql, params = []) {\n    try {\n        const pool = getPool();\n        const [rows] = await pool.execute(sql, params);\n        return rows;\n    } catch (error) {\n        console.error(\"Database query error:\", error);\n        throw error;\n    }\n}\n// 生成ID (MySQL会自动生成，这里保留兼容性)\nfunction generateId(items) {\n    if (!items || items.length === 0) return 1;\n    return Math.max(...items.map((item)=>item.id || 0)) + 1;\n}\n// 管理员用户操作\nconst adminUsersDAO = {\n    async create (userData) {\n        try {\n            const { username , password , email , role =\"admin\"  } = userData;\n            const result = await query(\"INSERT INTO admin_users (username, password, email, role) VALUES (?, ?, ?, ?)\", [\n                username,\n                password,\n                email,\n                role\n            ]);\n            return result.insertId;\n        } catch (error) {\n            console.error(\"Error creating admin user:\", error);\n            throw error;\n        }\n    },\n    async getByUsername (username) {\n        try {\n            const rows = await query('SELECT * FROM admin_users WHERE username = ? AND status = \"active\"', [\n                username\n            ]);\n            return rows.length > 0 ? rows[0] : null;\n        } catch (error) {\n            console.error(\"Error getting admin user by username:\", error);\n            throw error;\n        }\n    },\n    async getById (id) {\n        try {\n            const rows = await query('SELECT * FROM admin_users WHERE id = ? AND status = \"active\"', [\n                id\n            ]);\n            return rows.length > 0 ? rows[0] : null;\n        } catch (error) {\n            console.error(\"Error getting admin user by id:\", error);\n            throw error;\n        }\n    },\n    async updateLastLogin (id) {\n        try {\n            await query(\"UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?\", [\n                id\n            ]);\n            return true;\n        } catch (error) {\n            console.error(\"Error updating last login:\", error);\n            throw error;\n        }\n    },\n    async getAll () {\n        try {\n            const rows = await query(\"SELECT id, username, email, role, status, last_login, created_at FROM admin_users ORDER BY created_at DESC\");\n            return rows;\n        } catch (error) {\n            console.error(\"Error getting all admin users:\", error);\n            return [];\n        }\n    },\n    async updatePassword (id, newPassword) {\n        try {\n            await query(\"UPDATE admin_users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?\", [\n                newPassword,\n                id\n            ]);\n            return true;\n        } catch (error) {\n            console.error(\"Error updating password:\", error);\n            throw error;\n        }\n    },\n    async updateProfile (id, updates) {\n        try {\n            const { password , ...profileUpdates } = updates;\n            const fields = [];\n            const values = [];\n            Object.keys(profileUpdates).forEach((key)=>{\n                if (profileUpdates[key] !== undefined) {\n                    fields.push(`${key} = ?`);\n                    values.push(profileUpdates[key]);\n                }\n            });\n            if (fields.length > 0) {\n                fields.push(\"updated_at = CURRENT_TIMESTAMP\");\n                values.push(id);\n                const sql = `UPDATE admin_users SET ${fields.join(\", \")} WHERE id = ?`;\n                await query(sql, values);\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            throw error;\n        }\n    }\n};\n// 网站配置操作\nconst siteConfigDAO = {\n    async getAll () {\n        try {\n            const rows = await query(\"SELECT config_key, config_value FROM site_config\");\n            const config = {};\n            rows.forEach((row)=>{\n                config[row.config_key] = row.config_value;\n            });\n            // 如果没有配置，返回空值\n            if (Object.keys(config).length === 0) {\n                return {\n                    title: \"\",\n                    description: \"\",\n                    logo: \"\"\n                };\n            }\n            return config;\n        } catch (error) {\n            console.error(\"Error getting site config:\", error);\n            return {\n                title: \"\",\n                description: \"\",\n                logo: \"\"\n            };\n        }\n    },\n    async get (key) {\n        try {\n            const rows = await query(\"SELECT config_value FROM site_config WHERE config_key = ?\", [\n                key\n            ]);\n            return rows.length > 0 ? rows[0].config_value : null;\n        } catch (error) {\n            console.error(\"Error getting site config key:\", error);\n            return null;\n        }\n    },\n    async set (key, value) {\n        try {\n            // 将boolean值转换为字符串存储\n            const stringValue = typeof value === \"boolean\" ? value.toString() : value;\n            await query(\"INSERT INTO site_config (config_key, config_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE config_value = VALUES(config_value)\", [\n                key,\n                stringValue\n            ]);\n        } catch (error) {\n            console.error(\"Error setting site config:\", error);\n            throw error;\n        }\n    },\n    async setMultiple (configs) {\n        try {\n            for (const [key, value] of Object.entries(configs)){\n                // 将boolean值转换为字符串存储\n                const stringValue = typeof value === \"boolean\" ? value.toString() : value;\n                await this.set(key, stringValue);\n            }\n        } catch (error) {\n            console.error(\"Error setting multiple site configs:\", error);\n            throw error;\n        }\n    }\n};\n// 导航菜单操作\nconst navigationDAO = {\n    async getAll () {\n        try {\n            const rows = await query(\"SELECT * FROM navigation ORDER BY sort_order ASC, id ASC\");\n            return rows;\n        } catch (error) {\n            console.error(\"Error getting navigation:\", error);\n            return [];\n        }\n    },\n    async getVisible () {\n        try {\n            const rows = await query(\"SELECT * FROM navigation WHERE visible = TRUE ORDER BY sort_order ASC, id ASC\");\n            return rows;\n        } catch (error) {\n            console.error(\"Error getting visible navigation:\", error);\n            return [];\n        }\n    },\n    async create (data) {\n        try {\n            const result = await query(\"INSERT INTO navigation (name, href, visible, sort_order) VALUES (?, ?, ?, ?)\", [\n                data.name,\n                data.href,\n                data.visible !== false,\n                data.sort_order || 0\n            ]);\n            return result.insertId;\n        } catch (error) {\n            console.error(\"Error creating navigation:\", error);\n            throw error;\n        }\n    },\n    async update (id, data) {\n        try {\n            const updateFields = [];\n            const updateValues = [];\n            if (data.name !== undefined) {\n                updateFields.push(\"name = ?\");\n                updateValues.push(data.name);\n            }\n            if (data.href !== undefined) {\n                updateFields.push(\"href = ?\");\n                updateValues.push(data.href);\n            }\n            if (data.visible !== undefined) {\n                updateFields.push(\"visible = ?\");\n                updateValues.push(data.visible);\n            }\n            if (data.sort_order !== undefined) {\n                updateFields.push(\"sort_order = ?\");\n                updateValues.push(data.sort_order);\n            }\n            if (updateFields.length > 0) {\n                updateValues.push(parseInt(id));\n                await query(`UPDATE navigation SET ${updateFields.join(\", \")} WHERE id = ?`, updateValues);\n            }\n        } catch (error) {\n            console.error(\"Error updating navigation:\", error);\n            throw error;\n        }\n    },\n    async delete (id) {\n        try {\n            await query(\"DELETE FROM navigation WHERE id = ?\", [\n                parseInt(id)\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting navigation:\", error);\n            throw error;\n        }\n    }\n};\n// 分类操作\nconst categoriesDAO = {\n    async getAll () {\n        try {\n            const rows = await query(\"SELECT * FROM categories ORDER BY sort_order ASC, id ASC\");\n            return rows;\n        } catch (error) {\n            console.error(\"Error getting categories:\", error);\n            return [];\n        }\n    },\n    async getVisible () {\n        try {\n            const rows = await query(\"SELECT * FROM categories WHERE visible = TRUE ORDER BY sort_order ASC, id ASC\");\n            return rows;\n        } catch (error) {\n            console.error(\"Error getting visible categories:\", error);\n            return [];\n        }\n    },\n    async getById (id) {\n        try {\n            const rows = await query(\"SELECT * FROM categories WHERE id = ?\", [\n                parseInt(id)\n            ]);\n            return rows.length > 0 ? rows[0] : null;\n        } catch (error) {\n            console.error(\"Error getting category by id:\", error);\n            return null;\n        }\n    },\n    async create (data) {\n        try {\n            const result = await query(\"INSERT INTO categories (name, icon, count, visible, sort_order) VALUES (?, ?, ?, ?, ?)\", [\n                data.name,\n                data.icon,\n                0,\n                data.visible !== false,\n                data.sort_order || 0\n            ]);\n            return result.insertId;\n        } catch (error) {\n            console.error(\"Error creating category:\", error);\n            throw error;\n        }\n    },\n    async update (id, data) {\n        try {\n            const updateFields = [];\n            const updateValues = [];\n            if (data.name !== undefined) {\n                updateFields.push(\"name = ?\");\n                updateValues.push(data.name);\n            }\n            if (data.icon !== undefined) {\n                updateFields.push(\"icon = ?\");\n                updateValues.push(data.icon);\n            }\n            // 移除count字段的手动更新，count只能通过updateCount方法自动计算\n            if (data.visible !== undefined) {\n                updateFields.push(\"visible = ?\");\n                updateValues.push(data.visible);\n            }\n            if (data.sort_order !== undefined) {\n                updateFields.push(\"sort_order = ?\");\n                updateValues.push(data.sort_order);\n            }\n            if (updateFields.length > 0) {\n                updateValues.push(parseInt(id));\n                await query(`UPDATE categories SET ${updateFields.join(\", \")} WHERE id = ?`, updateValues);\n            }\n        } catch (error) {\n            console.error(\"Error updating category:\", error);\n            throw error;\n        }\n    },\n    async delete (id) {\n        try {\n            await query(\"DELETE FROM categories WHERE id = ?\", [\n                parseInt(id)\n            ]);\n        } catch (error) {\n            console.error(\"Error deleting category:\", error);\n            throw error;\n        }\n    },\n    async updateCount (id) {\n        try {\n            const countResult = await query(\"SELECT COUNT(*) as count FROM cards WHERE category_id = ? AND visible = TRUE\", [\n                parseInt(id)\n            ]);\n            const count = countResult[0].count;\n            // 直接更新count字段，绕过update方法的限制\n            await query(\"UPDATE categories SET count = ? WHERE id = ?\", [\n                count,\n                parseInt(id)\n            ]);\n        } catch (error) {\n            console.error(\"Error updating category count:\", error);\n            throw error;\n        }\n    },\n    // 修复所有分类的计数\n    async fixAllCounts () {\n        try {\n            const categories = await this.getAll();\n            for (const category of categories){\n                await this.updateCount(category.id);\n            }\n            console.log(\"All category counts have been fixed\");\n        } catch (error) {\n            console.error(\"Error fixing category counts:\", error);\n            throw error;\n        }\n    }\n};\n// 卡牌操作\nconst cardsDAO = {\n    async getAll () {\n        try {\n            const rows = await query(\"SELECT * FROM cards ORDER BY created_at DESC\");\n            // 安全解析JSON字段的函数\n            const safeJSONParse = (jsonString, defaultValue = {})=>{\n                if (!jsonString) return defaultValue;\n                try {\n                    const parsed = JSON.parse(jsonString);\n                    // 如果解析结果是字符串，可能是双重编码，再次解析\n                    if (typeof parsed === \"string\") {\n                        return JSON.parse(parsed);\n                    }\n                    return parsed;\n                } catch (error) {\n                    console.error(\"JSON parse error:\", error, \"for string:\", jsonString);\n                    return defaultValue;\n                }\n            };\n            // 解析JSON字段\n            return rows.map((card)=>({\n                    ...card,\n                    images: safeJSONParse(card.images, []),\n                    specifications: safeJSONParse(card.specifications, {}),\n                    seller: safeJSONParse(card.seller_info, null),\n                    // 兼容性字段映射，确保数值类型\n                    price: card.price ? parseFloat(card.price) : 0,\n                    originalPrice: card.original_price ? parseFloat(card.original_price) : null,\n                    estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,\n                    expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,\n                    endTime: card.end_time,\n                    condition: card.condition_desc,\n                    categoryId: card.category_id,\n                    createdAt: card.created_at,\n                    updatedAt: card.updated_at,\n                    // 状态值转换：将中文状态转换为英文键值\n                    status: card.status === \"立即购买\" ? \"buyNow\" : card.status === \"竞拍中\" ? \"auctioning\" : card.status,\n                    // 转换布尔值\n                    visible: Boolean(card.visible),\n                    featured: Boolean(card.featured)\n                }));\n        } catch (error) {\n            console.error(\"Error getting cards:\", error);\n            return [];\n        }\n    },\n    async getVisible () {\n        try {\n            const rows = await query(\"SELECT * FROM cards WHERE visible = 1 ORDER BY created_at DESC\");\n            return rows.map((card)=>({\n                    ...card,\n                    images: card.images ? JSON.parse(card.images) : [],\n                    specifications: card.specifications ? JSON.parse(card.specifications) : {},\n                    seller: card.seller_info ? JSON.parse(card.seller_info) : null,\n                    // 兼容性字段映射，确保数值类型\n                    price: card.price ? parseFloat(card.price) : 0,\n                    originalPrice: card.original_price ? parseFloat(card.original_price) : null,\n                    estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,\n                    expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,\n                    fixedPrice: card.fixed_price ? parseFloat(card.fixed_price) : null,\n                    sellMultiplier: card.sell_multiplier ? parseFloat(Number(card.sell_multiplier).toFixed(3)) : 1.050,\n                    endTime: card.end_time,\n                    condition: card.condition_desc,\n                    categoryId: card.category_id,\n                    createdAt: card.created_at,\n                    updatedAt: card.updated_at,\n                    // 状态值转换：将中文状态转换为英文键值\n                    status: card.status === \"立即购买\" ? \"buyNow\" : card.status === \"竞拍中\" ? \"auctioning\" : card.status,\n                    // 转换布尔值\n                    visible: Boolean(card.visible),\n                    featured: Boolean(card.featured)\n                }));\n        } catch (error) {\n            console.error(\"Error getting visible cards:\", error);\n            return [];\n        }\n    },\n    async getFeatured () {\n        try {\n            const rows = await query(\"SELECT * FROM cards WHERE visible = 1 AND featured = 1 ORDER BY created_at DESC\");\n            return rows.map((card)=>({\n                    ...card,\n                    images: card.images ? JSON.parse(card.images) : [],\n                    specifications: card.specifications ? JSON.parse(card.specifications) : {},\n                    seller: card.seller_info ? JSON.parse(card.seller_info) : null,\n                    // 兼容性字段映射，确保数值类型\n                    price: card.price ? parseFloat(card.price) : 0,\n                    originalPrice: card.original_price ? parseFloat(card.original_price) : null,\n                    estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,\n                    expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,\n                    endTime: card.end_time,\n                    condition: card.condition_desc,\n                    categoryId: card.category_id,\n                    createdAt: card.created_at,\n                    updatedAt: card.updated_at,\n                    // 状态值转换：将中文状态转换为英文键值\n                    status: card.status === \"立即购买\" ? \"buyNow\" : card.status === \"竞拍中\" ? \"auctioning\" : card.status,\n                    // 转换布尔值\n                    visible: Boolean(card.visible),\n                    featured: Boolean(card.featured)\n                }));\n        } catch (error) {\n            console.error(\"Error getting featured cards:\", error);\n            return [];\n        }\n    },\n    async getById (id) {\n        try {\n            const parsedId = parseInt(id);\n            if (isNaN(parsedId)) {\n                return null;\n            }\n            const rows = await query(\"SELECT * FROM cards WHERE id = ?\", [\n                parsedId\n            ]);\n            if (rows.length === 0) {\n                return null;\n            }\n            const card = rows[0];\n            // 安全解析JSON字段的函数\n            const safeJSONParse = (jsonString, defaultValue = {})=>{\n                if (!jsonString) return defaultValue;\n                try {\n                    const parsed = JSON.parse(jsonString);\n                    // 如果解析结果是字符串，可能是双重编码，再次解析\n                    if (typeof parsed === \"string\") {\n                        return JSON.parse(parsed);\n                    }\n                    return parsed;\n                } catch (error) {\n                    console.error(\"JSON parse error:\", error, \"for string:\", jsonString);\n                    return defaultValue;\n                }\n            };\n            return {\n                ...card,\n                images: safeJSONParse(card.images, []),\n                specifications: safeJSONParse(card.specifications, {}),\n                seller: safeJSONParse(card.seller_info, null),\n                // 兼容性字段映射，确保数值类型\n                price: card.price ? parseFloat(card.price) : 0,\n                originalPrice: card.original_price ? parseFloat(card.original_price) : null,\n                estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,\n                expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,\n                fixedPrice: card.fixed_price ? parseFloat(card.fixed_price) : null,\n                sellMultiplier: card.sell_multiplier ? parseFloat(Number(card.sell_multiplier).toFixed(3)) : 1.050,\n                endTime: card.end_time,\n                condition: card.condition_desc,\n                categoryId: card.category_id,\n                createdAt: card.created_at,\n                updatedAt: card.updated_at,\n                // 状态值转换：将中文状态转换为英文键值\n                status: card.status === \"立即购买\" ? \"buyNow\" : card.status === \"竞拍中\" ? \"auctioning\" : card.status,\n                // 转换布尔值\n                visible: Boolean(card.visible),\n                featured: Boolean(card.featured)\n            };\n        } catch (error) {\n            console.error(\"Error getting card by id:\", error);\n            return null;\n        }\n    },\n    async getByCategory (categoryId) {\n        try {\n            const rows = await query(\"SELECT * FROM cards WHERE category_id = ? AND visible = 1 ORDER BY created_at DESC\", [\n                parseInt(categoryId)\n            ]);\n            return rows.map((card)=>({\n                    ...card,\n                    images: card.images ? JSON.parse(card.images) : [],\n                    specifications: card.specifications ? JSON.parse(card.specifications) : {},\n                    seller: card.seller_info ? JSON.parse(card.seller_info) : null,\n                    // 兼容性字段映射，确保数值类型\n                    price: card.price ? parseFloat(card.price) : 0,\n                    originalPrice: card.original_price ? parseFloat(card.original_price) : null,\n                    estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,\n                    expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,\n                    fixedPrice: card.fixed_price ? parseFloat(card.fixed_price) : null,\n                    sellMultiplier: card.sell_multiplier ? parseFloat(Number(card.sell_multiplier).toFixed(3)) : 1.050,\n                    endTime: card.end_time,\n                    condition: card.condition_desc,\n                    categoryId: card.category_id,\n                    createdAt: card.created_at,\n                    updatedAt: card.updated_at,\n                    // 状态值转换：将中文状态转换为英文键值\n                    status: card.status === \"立即购买\" ? \"buyNow\" : card.status === \"竞拍中\" ? \"auctioning\" : card.status,\n                    // 转换布尔值\n                    visible: Boolean(card.visible),\n                    featured: Boolean(card.featured)\n                }));\n        } catch (error) {\n            console.error(\"Error getting cards by category:\", error);\n            return [];\n        }\n    },\n    async create (data) {\n        try {\n            // 处理字段映射和JSON序列化，确保undefined转换为null\n            const cardData = {\n                name: data.name || null,\n                subtitle: data.subtitle || null,\n                year: data.year || null,\n                grade: data.grade || null,\n                price: data.price || 0,\n                original_price: data.originalPrice || data.original_price || null,\n                estimated_value: data.estimatedValue || data.estimated_value || null,\n                expected_sell_price: data.expectedSellPrice || data.expected_sell_price || null,\n                fixed_price: data.fixedPrice || data.fixed_price || null,\n                sell_multiplier: data.sellMultiplier || data.sell_multiplier || 1.050,\n                status: data.status || \"立即购买\",\n                end_time: data.endTime || data.end_time || null,\n                condition_desc: data.condition || data.condition_desc || null,\n                category_id: data.categoryId || data.category_id || null,\n                visible: data.visible !== false,\n                featured: data.featured === true,\n                description: data.description || null,\n                images: JSON.stringify(data.images || []),\n                specifications: JSON.stringify(data.specifications || {}),\n                seller_info: JSON.stringify(data.seller_info || {})\n            };\n            const result = await query(`INSERT INTO cards (\n          name, subtitle, year, grade, price, original_price, estimated_value, expected_sell_price,\n          fixed_price, sell_multiplier, status, end_time,\n          condition_desc, category_id, visible, featured, description, images,\n          specifications, seller_info\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [\n                cardData.name,\n                cardData.subtitle,\n                cardData.year,\n                cardData.grade,\n                cardData.price,\n                cardData.original_price,\n                cardData.estimated_value,\n                cardData.expected_sell_price,\n                cardData.fixed_price,\n                cardData.sell_multiplier,\n                cardData.status,\n                cardData.end_time,\n                cardData.condition_desc,\n                cardData.category_id,\n                cardData.visible,\n                cardData.featured,\n                cardData.description,\n                cardData.images,\n                cardData.specifications,\n                cardData.seller_info\n            ]);\n            // 更新分类计数\n            if (cardData.category_id) {\n                await categoriesDAO.updateCount(cardData.category_id);\n            }\n            return result.insertId;\n        } catch (error) {\n            console.error(\"Error creating card:\", error);\n            throw error;\n        }\n    },\n    async update (id, data) {\n        try {\n            // 获取更新前的卡牌信息，用于分类计数更新\n            const oldCard = await this.getById(id);\n            const updateFields = [];\n            const updateValues = [];\n            if (data.name !== undefined) {\n                updateFields.push(\"name = ?\");\n                updateValues.push(data.name);\n            }\n            if (data.subtitle !== undefined) {\n                updateFields.push(\"subtitle = ?\");\n                updateValues.push(data.subtitle);\n            }\n            if (data.year !== undefined) {\n                updateFields.push(\"year = ?\");\n                updateValues.push(data.year);\n            }\n            if (data.grade !== undefined) {\n                updateFields.push(\"grade = ?\");\n                updateValues.push(data.grade);\n            }\n            if (data.price !== undefined) {\n                updateFields.push(\"price = ?\");\n                updateValues.push(data.price);\n            }\n            if (data.originalPrice !== undefined || data.original_price !== undefined) {\n                updateFields.push(\"original_price = ?\");\n                updateValues.push(data.originalPrice || data.original_price);\n            }\n            if (data.estimatedValue !== undefined || data.estimated_value !== undefined) {\n                updateFields.push(\"estimated_value = ?\");\n                updateValues.push(data.estimatedValue || data.estimated_value);\n            }\n            if (data.expectedSellPrice !== undefined || data.expected_sell_price !== undefined) {\n                updateFields.push(\"expected_sell_price = ?\");\n                updateValues.push(data.expectedSellPrice || data.expected_sell_price);\n            }\n            if (data.fixedPrice !== undefined || data.fixed_price !== undefined) {\n                updateFields.push(\"fixed_price = ?\");\n                updateValues.push(data.fixedPrice || data.fixed_price);\n            }\n            if (data.sellMultiplier !== undefined || data.sell_multiplier !== undefined) {\n                updateFields.push(\"sell_multiplier = ?\");\n                updateValues.push(data.sellMultiplier || data.sell_multiplier);\n            }\n            if (data.status !== undefined) {\n                updateFields.push(\"status = ?\");\n                updateValues.push(data.status);\n            }\n            if (data.endTime !== undefined || data.end_time !== undefined) {\n                updateFields.push(\"end_time = ?\");\n                updateValues.push(data.endTime || data.end_time);\n            }\n            if (data.condition !== undefined || data.condition_desc !== undefined) {\n                updateFields.push(\"condition_desc = ?\");\n                updateValues.push(data.condition || data.condition_desc);\n            }\n            if (data.categoryId !== undefined || data.category_id !== undefined) {\n                updateFields.push(\"category_id = ?\");\n                updateValues.push(data.categoryId || data.category_id);\n            }\n            if (data.visible !== undefined) {\n                updateFields.push(\"visible = ?\");\n                updateValues.push(data.visible);\n            }\n            if (data.featured !== undefined) {\n                updateFields.push(\"featured = ?\");\n                updateValues.push(data.featured);\n            }\n            if (data.description !== undefined) {\n                updateFields.push(\"description = ?\");\n                updateValues.push(data.description);\n            }\n            if (data.images !== undefined) {\n                updateFields.push(\"images = ?\");\n                updateValues.push(JSON.stringify(data.images));\n            }\n            if (data.specifications !== undefined) {\n                updateFields.push(\"specifications = ?\");\n                updateValues.push(JSON.stringify(data.specifications));\n            }\n            if (updateFields.length > 0) {\n                updateValues.push(parseInt(id));\n                await query(`UPDATE cards SET ${updateFields.join(\", \")} WHERE id = ?`, updateValues);\n                // 更新分类计数 - 修复逻辑\n                const needsCountUpdate = data.categoryId !== undefined || data.category_id !== undefined || data.visible !== undefined;\n                if (needsCountUpdate && oldCard) {\n                    const newCategoryId = data.categoryId || data.category_id;\n                    const oldCategoryId = oldCard.categoryId;\n                    // 如果分类发生变化，需要更新新旧两个分类的计数\n                    if (newCategoryId !== undefined && newCategoryId !== oldCategoryId) {\n                        // 更新新分类计数\n                        await categoriesDAO.updateCount(newCategoryId);\n                        // 更新旧分类计数\n                        if (oldCategoryId) {\n                            await categoriesDAO.updateCount(oldCategoryId);\n                        }\n                    } else if (data.visible !== undefined && oldCard.categoryId) {\n                        // 如果只是可见性发生变化，更新当前分类计数\n                        await categoriesDAO.updateCount(oldCard.categoryId);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error updating card:\", error);\n            throw error;\n        }\n    },\n    async delete (id) {\n        try {\n            // 获取卡牌信息以便更新分类计数\n            const card = await this.getById(id);\n            await query(\"DELETE FROM cards WHERE id = ?\", [\n                parseInt(id)\n            ]);\n            // 更新分类计数\n            if (card && card.categoryId) {\n                await categoriesDAO.updateCount(card.categoryId);\n            }\n        } catch (error) {\n            console.error(\"Error deleting card:\", error);\n            throw error;\n        }\n    },\n    async search (keyword) {\n        try {\n            const rows = await query(`SELECT * FROM cards\n         WHERE visible = TRUE\n         AND (name LIKE ? OR subtitle LIKE ? OR description LIKE ?)\n         ORDER BY created_at DESC`, [\n                `%${keyword}%`,\n                `%${keyword}%`,\n                `%${keyword}%`\n            ]);\n            return rows.map((card)=>({\n                    ...card,\n                    images: card.images ? JSON.parse(card.images) : [],\n                    specifications: card.specifications ? JSON.parse(card.specifications) : {},\n                    seller_info: card.seller_info ? JSON.parse(card.seller_info) : {},\n                    // 兼容性字段映射，确保数值类型\n                    price: card.price ? parseFloat(card.price) : 0,\n                    originalPrice: card.original_price ? parseFloat(card.original_price) : null,\n                    estimatedValue: card.estimated_value ? parseFloat(card.estimated_value) : null,\n                    expectedSellPrice: card.expected_sell_price ? parseFloat(card.expected_sell_price) : null,\n                    endTime: card.end_time,\n                    condition: card.condition_desc,\n                    categoryId: card.category_id,\n                    seller: card.seller_info ? JSON.parse(card.seller_info) : {},\n                    createdAt: card.created_at,\n                    updatedAt: card.updated_at\n                }));\n        } catch (error) {\n            console.error(\"Error searching cards:\", error);\n            return [];\n        }\n    }\n};\n// 初始化数据库表\nasync function initDatabase() {\n    try {\n        const pool = getPool();\n        console.log(\"Initializing MySQL database...\");\n        // 创建数据库（如果不存在）\n        await pool.query(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);\n        // 使用数据库\n        await pool.query(`USE ${dbConfig.database}`);\n        // 创建管理员用户表\n        await pool.query(`\n      CREATE TABLE IF NOT EXISTS admin_users (\n        id INT PRIMARY KEY AUTO_INCREMENT,\n        username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',\n        password VARCHAR(255) NOT NULL COMMENT '密码哈希',\n        email VARCHAR(100) COMMENT '邮箱',\n        role VARCHAR(20) DEFAULT 'admin' COMMENT '角色',\n        status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',\n        last_login TIMESTAMP NULL COMMENT '最后登录时间',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n        INDEX idx_username (username),\n        INDEX idx_status (status)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表'\n    `);\n        // 创建网站配置表\n        await pool.query(`\n      CREATE TABLE IF NOT EXISTS site_config (\n        id INT PRIMARY KEY AUTO_INCREMENT,\n        config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键名',\n        config_value TEXT COMMENT '配置值',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n        INDEX idx_config_key (config_key)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站配置表'\n    `);\n        // 创建导航菜单表\n        await pool.query(`\n      CREATE TABLE IF NOT EXISTS navigation (\n        id INT PRIMARY KEY AUTO_INCREMENT,\n        name VARCHAR(100) NOT NULL COMMENT '菜单名称',\n        href VARCHAR(255) NOT NULL COMMENT '链接地址',\n        visible BOOLEAN DEFAULT TRUE COMMENT '是否可见',\n        sort_order INT DEFAULT 0 COMMENT '排序顺序',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n        INDEX idx_visible_sort (visible, sort_order)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导航菜单表'\n    `);\n        // 创建分类表\n        await pool.query(`\n      CREATE TABLE IF NOT EXISTS categories (\n        id INT PRIMARY KEY AUTO_INCREMENT,\n        name VARCHAR(100) NOT NULL COMMENT '分类名称',\n        icon VARCHAR(100) COMMENT '图标类名',\n        count INT DEFAULT 0 COMMENT '卡牌数量',\n        visible BOOLEAN DEFAULT TRUE COMMENT '是否可见',\n        sort_order INT DEFAULT 0 COMMENT '排序顺序',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n        INDEX idx_visible_sort (visible, sort_order),\n        INDEX idx_name (name)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡牌分类表'\n    `);\n        // 创建卡牌表\n        await pool.query(`\n      CREATE TABLE IF NOT EXISTS cards (\n        id INT PRIMARY KEY AUTO_INCREMENT,\n        name VARCHAR(255) NOT NULL COMMENT '钱币名称',\n        subtitle VARCHAR(255) COMMENT '副标题',\n        year VARCHAR(10) COMMENT '发行年份',\n        grade VARCHAR(50) COMMENT '评级',\n        price DECIMAL(10,2) NOT NULL COMMENT '当前价格',\n        original_price DECIMAL(10,2) COMMENT '原价',\n        estimated_value DECIMAL(10,2) COMMENT '估值价格',\n        expected_sell_price DECIMAL(10,2) COMMENT '预期售价',\n        fixed_price DECIMAL(10,2) COMMENT '固定价格',\n        sell_multiplier DECIMAL(5,3) DEFAULT 1.050 COMMENT '抛售系数',\n        status VARCHAR(50) DEFAULT '立即购买' COMMENT '状态',\n        end_time VARCHAR(100) COMMENT '结束时间',\n        condition_desc VARCHAR(255) COMMENT '品相描述',\n        category_id INT COMMENT '分类ID',\n        visible BOOLEAN DEFAULT TRUE COMMENT '是否可见',\n        featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',\n        description TEXT COMMENT '详细描述',\n        images TEXT COMMENT '图片数组(JSON字符串)',\n        specifications TEXT COMMENT '规格参数(JSON字符串)',\n        seller_info TEXT COMMENT '卖家信息(JSON字符串)',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,\n        INDEX idx_category_visible (category_id, visible),\n        INDEX idx_featured_visible (featured, visible),\n        INDEX idx_status (status),\n        INDEX idx_price (price),\n        INDEX idx_estimated_value (estimated_value),\n        INDEX idx_expected_sell_price (expected_sell_price),\n        INDEX idx_fixed_price (fixed_price),\n        INDEX idx_sell_multiplier (sell_multiplier),\n        INDEX idx_created_at (created_at)\n      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡牌信息表'\n    `);\n        // 添加估值字段（如果表已存在）\n        try {\n            await pool.query(`\n        ALTER TABLE cards ADD COLUMN estimated_value DECIMAL(10,2) COMMENT '估值价格'\n      `);\n            console.log(\"Added estimated_value column to cards table\");\n        } catch (error) {\n            // 字段可能已存在，忽略错误\n            if (!error.message.includes(\"Duplicate column name\")) {\n                console.log(\"estimated_value column may already exist\");\n            }\n        }\n        // 添加预期售价字段（如果表已存在）\n        try {\n            await pool.query(`\n        ALTER TABLE cards ADD COLUMN expected_sell_price DECIMAL(10,2) COMMENT '预期售价'\n      `);\n            console.log(\"Added expected_sell_price column to cards table\");\n        } catch (error1) {\n            // 字段可能已存在，忽略错误\n            if (!error1.message.includes(\"Duplicate column name\")) {\n                console.log(\"expected_sell_price column may already exist\");\n            }\n        }\n        // 添加固定价格字段（如果表已存在）\n        try {\n            await pool.query(`\n        ALTER TABLE cards ADD COLUMN fixed_price DECIMAL(10,2) COMMENT '固定价格'\n      `);\n            console.log(\"Added fixed_price column to cards table\");\n        } catch (error2) {\n            // 字段可能已存在，忽略错误\n            if (!error2.message.includes(\"Duplicate column name\")) {\n                console.log(\"fixed_price column may already exist\");\n            }\n        }\n        // 添加抛售系数字段（如果表已存在）\n        try {\n            await pool.query(`\n        ALTER TABLE cards ADD COLUMN sell_multiplier DECIMAL(5,3) DEFAULT 1.050 COMMENT '抛售系数'\n      `);\n            console.log(\"Added sell_multiplier column to cards table\");\n        } catch (error3) {\n            // 字段可能已存在，忽略错误\n            if (!error3.message.includes(\"Duplicate column name\")) {\n                console.log(\"sell_multiplier column may already exist\");\n            }\n        }\n        console.log(\"MySQL database tables created successfully\");\n        return true;\n    } catch (error4) {\n        console.error(\"MySQL database initialization error:\", error4);\n        throw error4;\n    }\n}\n// 填充初始数据（仅创建必要的基础结构）\nasync function seedDatabase() {\n    try {\n        console.log(\"Setting up basic database structure...\");\n        // 仅初始化空的网站配置，让用户自己设置\n        const existingConfig = await siteConfigDAO.get(\"title\");\n        if (!existingConfig) {\n            await siteConfigDAO.setMultiple({\n                title: \"\",\n                description: \"\",\n                logo: \"\",\n                whatsapp_enabled: \"false\",\n                whatsapp_phone: \"\",\n                whatsapp_message: \"您好！我想咨询一下您的古币纸币。\",\n                whatsapp_tooltip: \"需要帮助？在WhatsApp上与我们聊天！\"\n            });\n            console.log(\"Basic site configuration structure created\");\n        }\n        console.log(\"Basic database structure setup completed\");\n        return true;\n    } catch (error) {\n        console.error(\"MySQL database seed error:\", error);\n        throw error;\n    }\n}\nmodule.exports = {\n    adminUsersDAO,\n    siteConfigDAO,\n    navigationDAO,\n    categoriesDAO,\n    cardsDAO,\n    initDatabase,\n    seedDatabase,\n    query,\n    getPool\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/mysqlDatabase.js\n");

/***/ }),

/***/ "(api)/./pages/api/admin/cards.js":
/*!**********************************!*\
  !*** ./pages/api/admin/cards.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst { cardsDAO  } = __webpack_require__(/*! ../../../lib/mysqlDatabase.js */ \"(api)/./lib/mysqlDatabase.js\");\nconst { withAdminAuth  } = __webpack_require__(/*! ../../../lib/auth */ \"(api)/./lib/auth.js\");\nasync function handler(req, res) {\n    try {\n        switch(req.method){\n            case \"GET\":\n                const cards = await cardsDAO.getAll();\n                res.status(200).json(cards);\n                break;\n            case \"POST\":\n                const cardData = req.body;\n                // 验证必填字段\n                if (!cardData.name || !cardData.name.trim()) {\n                    return res.status(400).json({\n                        message: \"卡牌名称不能为空\"\n                    });\n                }\n                if (!cardData.category_id) {\n                    return res.status(400).json({\n                        message: \"请选择分类\"\n                    });\n                }\n                // 设置默认值\n                const newCardData = {\n                    name: cardData.name.trim(),\n                    subtitle: cardData.subtitle || \"\",\n                    description: cardData.description || \"\",\n                    price: parseFloat(cardData.price) || 0,\n                    original_price: parseFloat(cardData.original_price) || 0,\n                    category_id: parseInt(cardData.category_id),\n                    condition_desc: cardData.condition_desc || \"\",\n                    images: JSON.stringify(cardData.images || []),\n                    specifications: JSON.stringify(cardData.specifications || {}),\n                    seller_info: JSON.stringify(cardData.seller_info || {}),\n                    visible: cardData.visible !== undefined ? cardData.visible : true,\n                    featured: cardData.featured !== undefined ? cardData.featured : false,\n                    end_time: cardData.end_time || null,\n                    status: cardData.status || \"active\"\n                };\n                const newCardId = await cardsDAO.create(newCardData);\n                const newCard = await cardsDAO.getById(newCardId);\n                res.status(201).json(newCard);\n                break;\n            case \"PUT\":\n                const { id  } = req.query;\n                const updates = req.body;\n                if (!id) {\n                    return res.status(400).json({\n                        message: \"缺少卡牌ID\"\n                    });\n                }\n                // 处理JSON字段\n                if (updates.images && typeof updates.images === \"object\") {\n                    updates.images = JSON.stringify(updates.images);\n                }\n                if (updates.specifications && typeof updates.specifications === \"object\") {\n                    updates.specifications = JSON.stringify(updates.specifications);\n                }\n                await cardsDAO.update(parseInt(id), updates);\n                const updatedCard = await cardsDAO.getById(parseInt(id));\n                if (!updatedCard) {\n                    return res.status(404).json({\n                        message: \"卡牌不存在\"\n                    });\n                }\n                res.status(200).json(updatedCard);\n                break;\n            case \"DELETE\":\n                const { id: deleteId  } = req.query;\n                if (!deleteId) {\n                    return res.status(400).json({\n                        message: \"缺少卡牌ID\"\n                    });\n                }\n                await cardsDAO.delete(parseInt(deleteId));\n                res.status(200).json({\n                    message: \"卡牌删除成功\"\n                });\n                break;\n            default:\n                res.status(405).json({\n                    message: \"Method not allowed\"\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in admin cards API:\", error);\n        res.status(500).json({\n            message: \"Internal server error\",\n            error: error.message\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withAdminAuth(handler));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/cards.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/admin/cards.js"));
module.exports = __webpack_exports__;

})();