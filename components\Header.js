import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import LanguageSwitcher from './LanguageSwitcher';
import { t } from '../lib/i18n';

export default function Header({ siteConfig, currentPage }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef(null);

  // 点击外部关闭菜单
  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    }

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  // 使用默认值以防siteConfig未传入
  const config = siteConfig || {
    title: 'CoinMarket',
    logo: 'CoinMarket',
    navigation: []
  };

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* 左侧区域：菜单按钮和LOGO */}
          <div className="flex items-center space-x-4">
            {/* 三条横线菜单按钮 */}
            <div className="relative" ref={menuRef}>
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="flex items-center justify-center w-10 h-10 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                aria-label={t('header.menu')}
              >
                <div className="flex flex-col space-y-1">
                  <div className={`w-6 h-0.5 bg-gray-600 rounded-full transition-transform duration-200 ${isMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></div>
                  <div className={`w-6 h-0.5 bg-gray-600 rounded-full transition-opacity duration-200 ${isMenuOpen ? 'opacity-0' : ''}`}></div>
                  <div className={`w-6 h-0.5 bg-gray-600 rounded-full transition-transform duration-200 ${isMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></div>
                </div>
              </button>

              {/* 下拉菜单 */}
              {isMenuOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in">
                  {currentPage === 'about' ? (
                    // 在公司简介页面显示首页链接
                    <Link href="/">
                      <a
                        className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {t('nav.home')}
                      </a>
                    </Link>
                  ) : (
                    // 在其他页面显示公司简介链接
                    <Link href="/about">
                      <a
                        className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {t('nav.about')}
                      </a>
                    </Link>
                  )}
                </div>
              )}
            </div>

            {/* LOGO区域 - 现在靠左显示 */}
            <Link href="/">
              <a className="flex items-center hover:opacity-80 transition-opacity duration-200">
                <img
                  src="/logo.png"
                  alt={config.title || 'CoinMarket'}
                  className="h-8 sm:h-10 w-auto"
                />
              </a>
            </Link>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            {config.navigation && config.navigation.length > 0 ? (
              config.navigation.map((item) => (
                <Link key={item.id} href={item.href}>
                  <a className="text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap">
                    {item.name}
                  </a>
                </Link>
              ))
            ) : (
              // 默认导航菜单（作为后备）
              <>
                <Link href="/cards">
                  <a className="text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap">
                    {t('nav.allCoin')}
                  </a>
                </Link>
                <Link href="/auction">
                  <a className="text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap">
                    {t('nav.auction')}
                  </a>
                </Link>
                <Link href="/premium">
                  <a className="text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap">
                    {t('nav.premium')}
                  </a>
                </Link>
                <Link href="/buy-now">
                  <a className="text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap">
                    {t('nav.buyNow')}
                  </a>
                </Link>
                <Link href="/sports-cards">
                  <a className="text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap">
                    {t('nav.sportsCards')}
                  </a>
                </Link>
                <Link href="/trading-cards">
                  <a className="text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap">
                    Trading Cards
                  </a>
                </Link>
                <Link href="/test-valuation">
                  <a className="text-purple-600 hover:text-purple-700 transition-colors whitespace-nowrap font-medium">
                    Valuation
                  </a>
                </Link>
                <Link href="/more">
                  <a className="text-gray-700 hover:text-blue-600 transition-colors whitespace-nowrap">
                    More
                  </a>
                </Link>
              </>
            )}
          </nav>

          <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
            <div className="relative hidden sm:block">
              <input
                type="text"
                placeholder={t('header.searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-48 lg:w-80 px-4 py-2 pl-10 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center">
                <i className="ri-search-line text-gray-400 text-sm"></i>
              </div>
            </div>

            {/* 手机端搜索按钮 */}
            <button className="sm:hidden w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors">
              <i className="ri-search-line text-gray-600"></i>
            </button>

            {/* 语言切换器 */}
            <LanguageSwitcher />
          </div>
        </div>
      </div>
    </header>
  );
}
